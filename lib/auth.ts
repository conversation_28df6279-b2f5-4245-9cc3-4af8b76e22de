import { auth, currentUser } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';

export type UserRole = 'admin' | 'manager' | 'operator' | 'subcontractor' | 'member';

/**
 * Get the current authenticated user with role information
 * @param redirectTo - URL to redirect to if user is not authenticated
 * @returns User object with role information
 */
export async function getAuthenticatedUser(redirectTo = '/sign-in') {
  const session = await auth();
  const user = await currentUser();
  
  if (!session?.userId || !user) {
    redirect(redirectTo);
  }
  
  const role = (user.publicMetadata.role as string) || 'member';
  const companyName = user.publicMetadata.companyName as string;
  const onboardedAt = user.publicMetadata.onboardedAt as string;
  
  return {
    id: user.id,
    firstName: user.firstName,
    lastName: user.lastName,
    email: user.emailAddresses[0]?.emailAddress,
    role,
    companyName,
    onboardedAt,
    isOnboarded: !!onboardedAt,
  };
}

/**
 * Check if the current user has the required role
 * @param requiredRoles - Array of roles that are allowed
 * @param redirectTo - URL to redirect to if user doesn't have the required role
 * @returns User object with role information
 */
export async function checkUserRole(
  requiredRoles: UserRole[], 
  redirectTo = '/dashboard'
) {
  const user = await getAuthenticatedUser();
  
  if (!requiredRoles.includes(user.role as UserRole)) {
    redirect(redirectTo);
  }
  
  return user;
}

/**
 * Check if the user has completed onboarding
 * If not, redirect to onboarding page
 */
export async function checkOnboarding() {
  const user = await getAuthenticatedUser();
  
  if (!user.isOnboarded) {
    redirect('/onboarding');
  }
  
  return user;
}
