-- Add missing columns to projects table if they don't exist
ALTER TABLE "projects" 
  ADD COLUMN IF NOT EXISTS "start_date" date,
  ADD COLUMN IF NOT EXISTS "end_date" date,
  ADD COLUMN IF NOT EXISTS "status" varchar(20) DEFAULT 'active',
  ADD COLUMN IF NOT EXISTS "deleted_at" timestamp;

-- Add missing columns to cranes table if it exists
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'cranes') THEN
    ALTER TABLE "cranes" 
      ADD COLUMN IF NOT EXISTS "name" varchar(255),
      ADD COLUMN IF NOT EXISTS "model" varchar(100),
      ADD COLUMN IF NOT EXISTS "type" varchar(50),
      ADD COLUMN IF NOT EXISTS "capacity" decimal(10, 2),
      ADD COLUMN IF NOT EXISTS "height" decimal(10, 2),
      ADD COLUMN IF NOT EXISTS "reach" decimal(10, 2),
      ADD COLUMN IF NOT EXISTS "team_id" integer,
      ADD COLUMN IF NOT EXISTS "status" varchar(20) DEFAULT 'available',
      ADD COLUMN IF NOT EXISTS "last_maintenance_date" date,
      ADD COLUMN IF NOT EXISTS "next_maintenance_date" date,
      ADD COLUMN IF NOT EXISTS "created_at" timestamp DEFAULT now(),
      ADD COLUMN IF NOT EXISTS "updated_at" timestamp DEFAULT now(),
      ADD COLUMN IF NOT EXISTS "deleted_at" timestamp,
      ADD COLUMN IF NOT EXISTS "metadata" jsonb;
  ELSE
    -- Create cranes table if it doesn't exist
    CREATE TABLE IF NOT EXISTS "cranes" (
      "id" serial PRIMARY KEY NOT NULL,
      "name" varchar(255) NOT NULL,
      "model" varchar(100),
      "type" varchar(50) NOT NULL,
      "capacity" decimal(10, 2),
      "height" decimal(10, 2),
      "reach" decimal(10, 2),
      "team_id" integer NOT NULL,
      "status" varchar(20) DEFAULT 'available' NOT NULL,
      "last_maintenance_date" date,
      "next_maintenance_date" date,
      "created_at" timestamp DEFAULT now() NOT NULL,
      "updated_at" timestamp DEFAULT now() NOT NULL,
      "deleted_at" timestamp,
      "metadata" jsonb
    );
  END IF;
END $$;

-- Add missing columns to bookings table if it exists
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'bookings') THEN
    ALTER TABLE "bookings" 
      ADD COLUMN IF NOT EXISTS "crane_id" integer,
      ADD COLUMN IF NOT EXISTS "project_id" text,
      ADD COLUMN IF NOT EXISTS "start_date" timestamp,
      ADD COLUMN IF NOT EXISTS "end_date" timestamp,
      ADD COLUMN IF NOT EXISTS "created_by_id" integer,
      ADD COLUMN IF NOT EXISTS "status" varchar(20) DEFAULT 'scheduled',
      ADD COLUMN IF NOT EXISTS "notes" text,
      ADD COLUMN IF NOT EXISTS "created_at" timestamp DEFAULT now(),
      ADD COLUMN IF NOT EXISTS "updated_at" timestamp DEFAULT now(),
      ADD COLUMN IF NOT EXISTS "deleted_at" timestamp;
  ELSE
    -- Create bookings table if it doesn't exist
    CREATE TABLE IF NOT EXISTS "bookings" (
      "id" serial PRIMARY KEY NOT NULL,
      "crane_id" integer NOT NULL,
      "project_id" text NOT NULL,
      "start_date" timestamp NOT NULL,
      "end_date" timestamp NOT NULL,
      "created_by_id" integer NOT NULL,
      "status" varchar(20) DEFAULT 'scheduled' NOT NULL,
      "notes" text,
      "created_at" timestamp DEFAULT now() NOT NULL,
      "updated_at" timestamp DEFAULT now() NOT NULL,
      "deleted_at" timestamp
    );
  END IF;
END $$;

-- Add foreign key constraints if they don't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'cranes_team_id_teams_id_fk'
  ) THEN
    ALTER TABLE "cranes" 
    ADD CONSTRAINT "cranes_team_id_teams_id_fk" 
    FOREIGN KEY ("team_id") REFERENCES "teams"("id") 
    ON DELETE NO ACTION ON UPDATE NO ACTION;
  END IF;
EXCEPTION
  WHEN others THEN
    RAISE NOTICE 'Error adding foreign key constraint cranes_team_id_teams_id_fk: %', SQLERRM;
END $$;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'bookings_crane_id_cranes_id_fk'
  ) THEN
    ALTER TABLE "bookings" 
    ADD CONSTRAINT "bookings_crane_id_cranes_id_fk" 
    FOREIGN KEY ("crane_id") REFERENCES "cranes"("id") 
    ON DELETE NO ACTION ON UPDATE NO ACTION;
  END IF;
EXCEPTION
  WHEN others THEN
    RAISE NOTICE 'Error adding foreign key constraint bookings_crane_id_cranes_id_fk: %', SQLERRM;
END $$;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'bookings_created_by_id_users_id_fk'
  ) THEN
    ALTER TABLE "bookings" 
    ADD CONSTRAINT "bookings_created_by_id_users_id_fk" 
    FOREIGN KEY ("created_by_id") REFERENCES "users"("id") 
    ON DELETE NO ACTION ON UPDATE NO ACTION;
  END IF;
EXCEPTION
  WHEN others THEN
    RAISE NOTICE 'Error adding foreign key constraint bookings_created_by_id_users_id_fk: %', SQLERRM;
END $$;
