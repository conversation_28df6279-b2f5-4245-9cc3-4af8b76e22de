# This is an example of your .env file format, which pnpm db:setup will create.
# Note: this must be .env, not .env.local, without further configuration changes.

# Database
POSTGRES_URL=postgresql://***

# Stripe
STRIPE_SECRET_KEY=sk_test_***
STRIPE_WEBHOOK_SECRET=whsec_***
STRIPE_PUBLISHABLE_KEY=pk_test_***

# App
BASE_URL=http://localhost:3000

# Auth (Legacy - to be removed after Clerk migration)
AUTH_SECRET=***

# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_***
CLERK_SECRET_KEY=sk_test_***
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/dashboard
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/onboarding