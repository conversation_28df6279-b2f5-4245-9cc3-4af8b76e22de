'use client';

import { useUser, useAuth, SignIn<PERSON><PERSON>on, SignOutButton } from '@clerk/nextjs';
import Link from 'next/link';

export default function DebugAuthPage() {
  const { isLoaded, isSignedIn, user } = useUser();
  const { userId } = useAuth();

  return (
    <div className="p-8 space-y-4">
      <h1 className="text-2xl font-bold">Auth Debug Page</h1>

      <div className="space-y-2">
        <p><strong>Is Loaded:</strong> {isLoaded ? 'Yes' : 'No'}</p>
        <p><strong>Is Signed In:</strong> {isSignedIn ? 'Yes' : 'No'}</p>
        <p><strong>User ID:</strong> {userId || 'None'}</p>
        <p><strong>User Email:</strong> {user?.emailAddresses?.[0]?.emailAddress || 'None'}</p>
        <p><strong>User Name:</strong> {user?.firstName} {user?.lastName}</p>
      </div>

      <div className="space-x-4">
        {isSignedIn ? (
          <SignOutButton>
            <button className="bg-red-600 text-white px-4 py-2 rounded">
              Sign Out
            </button>
          </SignOutButton>
        ) : (
          <SignInButton mode="modal">
            <button className="bg-blue-600 text-white px-4 py-2 rounded">
              Sign In (Modal)
            </button>
          </SignInButton>
        )}
      </div>

      <div className="space-x-4">
        <Link href="/sign-in" className="bg-green-600 text-white px-4 py-2 rounded inline-block">
          Go to Sign In Page
        </Link>
        <Link href="/sign-up" className="bg-purple-600 text-white px-4 py-2 rounded inline-block">
          Go to Sign Up Page
        </Link>
      </div>

      <div className="mt-8">
        <h2 className="text-lg font-semibold">Environment Variables:</h2>
        <p><strong>Publishable Key:</strong> {process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY ? 'Present' : 'Missing'}</p>
        <p><strong>Publishable Key Value:</strong> {process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY}</p>
      </div>

      <div className="mt-8">
        <h2 className="text-lg font-semibold">Test Links:</h2>
        <div className="space-y-2">
          <Link href="/dashboard" className="block text-blue-600 hover:underline">
            Try Dashboard (should redirect if not signed in)
          </Link>
          <Link href="/" className="block text-blue-600 hover:underline">
            Go to Home Page
          </Link>
        </div>
      </div>
    </div>
  );
}
