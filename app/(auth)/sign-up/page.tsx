import { SignUp } from '@clerk/nextjs';

export default function SignUpPage() {
  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">
            Create your LiftrUP account
          </h1>
          <p className="mt-2 text-sm text-gray-600">
            Start managing your construction projects and crane scheduling
          </p>
        </div>
        <div className="mt-8">
          <SignUp
            appearance={{
              elements: {
                formButtonPrimary:
                  'bg-blue-600 hover:bg-blue-700 text-sm normal-case',
                card: 'rounded-lg shadow-md',
              },
            }}
            redirectUrl="/onboarding"
          />
        </div>
      </div>
    </div>
  );
}
