'use client';

import { useState } from 'react';
import { UserButton, useUser } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';

export default function UserProfile() {
  const { user, isLoaded } = useUser();
  const router = useRouter();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  
  if (!isLoaded || !user) {
    return <div className="h-8 w-8 rounded-full bg-gray-200 animate-pulse" />;
  }
  
  // Get user role and company from metadata
  const role = (user.publicMetadata.role as string) || 'member';
  const companyName = user.publicMetadata.companyName as string;
  
  // Format role for display (capitalize first letter)
  const formattedRole = role.charAt(0).toUpperCase() + role.slice(1);
  
  return (
    <div className="relative">
      <div 
        className="flex items-center gap-3 rounded-lg p-2 hover:bg-gray-100 cursor-pointer"
        onClick={() => setIsMenuOpen(!isMenuOpen)}
      >
        <div className="flex flex-col">
          <span className="text-sm font-medium">
            {user.firstName || user.emailAddresses[0].emailAddress.split('@')[0]}
          </span>
          <span className="text-xs text-gray-500">{formattedRole}</span>
        </div>
        <UserButton afterSignOutUrl="/" />
      </div>
      
      {isMenuOpen && (
        <div className="absolute right-0 mt-2 w-56 rounded-md border bg-white p-2 shadow-lg">
          <div className="mb-2 border-b pb-2">
            <p className="text-sm font-medium">{user.fullName || user.emailAddresses[0].emailAddress}</p>
            {companyName && (
              <p className="text-xs text-gray-500">{companyName}</p>
            )}
            <p className="mt-1 text-xs text-gray-500">{formattedRole}</p>
          </div>
          
          <div className="space-y-1">
            <button
              onClick={() => router.push('/dashboard/settings')}
              className="w-full rounded-md px-3 py-2 text-left text-sm hover:bg-gray-100"
            >
              Settings
            </button>
            <button
              onClick={() => router.push('/dashboard/help')}
              className="w-full rounded-md px-3 py-2 text-left text-sm hover:bg-gray-100"
            >
              Help & Support
            </button>
            <hr className="my-1" />
            <button
              onClick={() => {
                setIsMenuOpen(false);
                // Sign out is handled by UserButton
              }}
              className="w-full rounded-md px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50"
            >
              Sign Out
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
