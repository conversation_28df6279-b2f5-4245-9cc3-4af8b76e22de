'use client';

import { User } from '@clerk/nextjs/server';
import { UserButton } from '@clerk/nextjs';

type DashboardWelcomeProps = {
  user: User;
  role: string;
  companyName: string;
};

export default function DashboardWelcome({ user, role, companyName }: DashboardWelcomeProps) {
  // Format role for display (capitalize first letter)
  const formattedRole = role.charAt(0).toUpperCase() + role.slice(1);
  
  return (
    <div className="flex flex-col items-start justify-between gap-4 rounded-lg border bg-white p-6 shadow-sm md:flex-row md:items-center">
      <div>
        <h1 className="text-2xl font-bold">
          Welcome, {user.firstName || user.emailAddresses[0].emailAddress.split('@')[0]}
        </h1>
        <p className="text-gray-600">
          {companyName ? `${companyName} • ` : ''}{formattedRole}
        </p>
      </div>
      
      <div className="flex items-center gap-4">
        <div className="rounded-md bg-blue-50 px-3 py-1 text-sm text-blue-700">
          {formattedRole}
        </div>
        <UserButton afterSignOutUrl="/" />
      </div>
    </div>
  );
}
