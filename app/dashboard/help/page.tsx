import { currentUser } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import { checkOnboarding } from '@/lib/auth';

export default async function HelpPage() {
  // Ensure user is authenticated and has completed onboarding
  const user = await checkOnboarding();
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="mb-6 text-2xl font-bold">Help & Support</h1>
      
      <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
        <div className="md:col-span-1">
          <nav className="space-y-1">
            <a
              href="#getting-started"
              className="flex items-center rounded-md bg-blue-50 px-3 py-2 text-sm font-medium text-blue-700"
            >
              Getting Started
            </a>
            <a
              href="#faq"
              className="flex items-center rounded-md px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Frequently Asked Questions
            </a>
            <a
              href="#contact"
              className="flex items-center rounded-md px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Contact Support
            </a>
            <a
              href="#documentation"
              className="flex items-center rounded-md px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Documentation
            </a>
          </nav>
        </div>
        
        <div className="md:col-span-2">
          <div id="getting-started" className="rounded-lg border bg-white p-6 shadow-sm">
            <h2 className="mb-4 text-lg font-medium">Getting Started with LiftrUP</h2>
            
            <div className="space-y-4">
              <p className="text-gray-600">
                Welcome to LiftrUP, the construction management SaaS platform focused on crane scheduling. 
                Here's how to get started:
              </p>
              
              <div className="space-y-3">
                <div className="flex items-start">
                  <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-100 text-blue-600">
                    1
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium">Complete your profile</h3>
                    <p className="text-sm text-gray-500">
                      Make sure your profile information is up-to-date, including your company details.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-100 text-blue-600">
                    2
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium">Create or join a project</h3>
                    <p className="text-sm text-gray-500">
                      Navigate to the Projects section to create a new project or join an existing one.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-100 text-blue-600">
                    3
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium">Schedule crane operations</h3>
                    <p className="text-sm text-gray-500">
                      Use the scheduling tool to book crane time and avoid conflicts with other teams.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div id="faq" className="mt-8 rounded-lg border bg-white p-6 shadow-sm">
            <h2 className="mb-4 text-lg font-medium">Frequently Asked Questions</h2>
            
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium">How do I invite team members?</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Navigate to the Team section in your dashboard and click on "Invite Member". Enter their email address and select their role.
                </p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium">How do I resolve scheduling conflicts?</h3>
                <p className="mt-1 text-sm text-gray-500">
                  The system will automatically detect conflicts. You can view conflicts in the Schedule section and negotiate with other teams through the built-in messaging system.
                </p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium">Can I export scheduling data?</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Yes, you can export schedules in various formats including PDF, CSV, and calendar formats (iCal, Google Calendar).
                </p>
              </div>
            </div>
          </div>
          
          <div id="contact" className="mt-8 rounded-lg border bg-white p-6 shadow-sm">
            <h2 className="mb-4 text-lg font-medium">Contact Support</h2>
            
            <div className="space-y-4">
              <p className="text-gray-600">
                Our support team is available Monday through Friday, 9am to 5pm EST.
              </p>
              
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div className="rounded-md border border-gray-200 p-4">
                  <h3 className="text-sm font-medium">Email Support</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    <EMAIL>
                  </p>
                  <p className="mt-1 text-xs text-gray-400">
                    Response time: Within 24 hours
                  </p>
                </div>
                
                <div className="rounded-md border border-gray-200 p-4">
                  <h3 className="text-sm font-medium">Phone Support</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    +****************
                  </p>
                  <p className="mt-1 text-xs text-gray-400">
                    Available 9am-5pm EST, Mon-Fri
                  </p>
                </div>
              </div>
            </div>
          </div>
          
          <div id="documentation" className="mt-8 rounded-lg border bg-white p-6 shadow-sm">
            <h2 className="mb-4 text-lg font-medium">Documentation</h2>
            
            <div className="space-y-4">
              <p className="text-gray-600">
                Access our comprehensive documentation to learn more about LiftrUP features.
              </p>
              
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <a
                  href="#"
                  className="flex flex-col items-center rounded-md border border-gray-200 p-4 hover:bg-gray-50"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-8 w-8 text-blue-500"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                    />
                  </svg>
                  <h3 className="mt-2 text-sm font-medium">User Guide</h3>
                  <p className="mt-1 text-xs text-gray-500">
                    Complete guide to using LiftrUP
                  </p>
                </a>
                
                <a
                  href="#"
                  className="flex flex-col items-center rounded-md border border-gray-200 p-4 hover:bg-gray-50"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-8 w-8 text-blue-500"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                    />
                  </svg>
                  <h3 className="mt-2 text-sm font-medium">API Documentation</h3>
                  <p className="mt-1 text-xs text-gray-500">
                    Integrate with our API
                  </p>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
