import { currentUser } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import { checkOnboarding } from '@/lib/auth';

export default async function SettingsPage() {
  // Ensure user is authenticated and has completed onboarding
  const user = await checkOnboarding();
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="mb-6 text-2xl font-bold">Account Settings</h1>
      
      <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
        <div className="md:col-span-1">
          <nav className="space-y-1">
            <a
              href="#profile"
              className="flex items-center rounded-md bg-blue-50 px-3 py-2 text-sm font-medium text-blue-700"
            >
              Profile Information
            </a>
            <a
              href="#company"
              className="flex items-center rounded-md px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Company Details
            </a>
            <a
              href="#notifications"
              className="flex items-center rounded-md px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Notification Preferences
            </a>
            <a
              href="#security"
              className="flex items-center rounded-md px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Security
            </a>
          </nav>
        </div>
        
        <div className="md:col-span-2">
          <div id="profile" className="rounded-lg border bg-white p-6 shadow-sm">
            <h2 className="mb-4 text-lg font-medium">Profile Information</h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Full Name
                </label>
                <div className="mt-1 flex items-center">
                  <span className="block w-full rounded-md border border-gray-300 bg-gray-50 px-3 py-2 text-gray-500 sm:text-sm">
                    {user.firstName} {user.lastName}
                  </span>
                  <button
                    type="button"
                    className="ml-3 rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
                    onClick={() => window.open('https://accounts.clerk.dev/user/profile', '_blank')}
                  >
                    Edit
                  </button>
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  To change your name, you'll be redirected to Clerk account settings
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Email Address
                </label>
                <div className="mt-1 flex items-center">
                  <span className="block w-full rounded-md border border-gray-300 bg-gray-50 px-3 py-2 text-gray-500 sm:text-sm">
                    {user.email}
                  </span>
                  <button
                    type="button"
                    className="ml-3 rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
                    onClick={() => window.open('https://accounts.clerk.dev/user/profile', '_blank')}
                  >
                    Edit
                  </button>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Role
                </label>
                <div className="mt-1">
                  <span className="inline-flex rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700">
                    {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                  </span>
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  Contact your administrator to change your role
                </p>
              </div>
            </div>
          </div>
          
          <div id="company" className="mt-8 rounded-lg border bg-white p-6 shadow-sm">
            <h2 className="mb-4 text-lg font-medium">Company Details</h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Company Name
                </label>
                <div className="mt-1 flex items-center">
                  <span className="block w-full rounded-md border border-gray-300 bg-gray-50 px-3 py-2 text-gray-500 sm:text-sm">
                    {user.companyName || 'Not specified'}
                  </span>
                  <button
                    type="button"
                    className="ml-3 rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
                  >
                    Edit
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
