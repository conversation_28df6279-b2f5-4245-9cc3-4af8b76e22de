import { currentUser } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CalendarDays, MapPin, Users, Plus } from 'lucide-react';

// This would typically come from an API call
async function getProjects() {
  // TODO: Replace with actual API call to /api/projects
  return [
    {
      id: '1',
      name: 'Downtown Office Complex',
      description: 'Construction of a 25-story office building in the downtown core',
      location: { address: '123 Main St, Sydney NSW' },
      startDate: '2024-01-15',
      endDate: '2024-12-31',
      status: 'active',
      createdAt: '2024-01-01',
    },
    {
      id: '2', 
      name: 'Residential Tower Project',
      description: 'High-rise residential development with 200 units',
      location: { address: '456 Harbor View, Melbourne VIC' },
      startDate: '2024-03-01',
      endDate: '2025-06-30',
      status: 'active',
      createdAt: '2024-02-15',
    },
    {
      id: '3',
      name: 'Shopping Center Renovation',
      description: 'Major renovation and expansion of existing shopping center',
      location: { address: '789 Commerce Dr, Brisbane QLD' },
      startDate: '2024-02-01',
      endDate: '2024-08-15',
      status: 'completed',
      createdAt: '2024-01-20',
    },
  ];
}

function getStatusColor(status: string) {
  switch (status) {
    case 'active':
      return 'bg-green-100 text-green-800';
    case 'completed':
      return 'bg-blue-100 text-blue-800';
    case 'on-hold':
      return 'bg-yellow-100 text-yellow-800';
    case 'cancelled':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-AU', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
}

function ProjectCard({ project }: { project: any }) {
  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="space-y-1">
            <CardTitle className="text-lg">
              <Link 
                href={`/dashboard/projects/${project.id}`}
                className="hover:text-blue-600 transition-colors"
              >
                {project.name}
              </Link>
            </CardTitle>
            <Badge className={getStatusColor(project.status)}>
              {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-sm text-gray-600 line-clamp-2">
          {project.description}
        </p>
        
        <div className="space-y-2">
          <div className="flex items-center text-sm text-gray-500">
            <MapPin className="h-4 w-4 mr-2" />
            {project.location?.address || 'Location not specified'}
          </div>
          
          <div className="flex items-center text-sm text-gray-500">
            <CalendarDays className="h-4 w-4 mr-2" />
            {formatDate(project.startDate)} - {formatDate(project.endDate)}
          </div>
          
          <div className="flex items-center text-sm text-gray-500">
            <Users className="h-4 w-4 mr-2" />
            {/* TODO: Add team member count */}
            Team members: TBD
          </div>
        </div>
        
        <div className="flex justify-between items-center pt-2">
          <span className="text-xs text-gray-400">
            Created {formatDate(project.createdAt)}
          </span>
          <Link href={`/dashboard/projects/${project.id}`}>
            <Button variant="outline" size="sm">
              View Details
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}

export default async function ProjectsPage() {
  const user = await currentUser();
  
  if (!user) {
    redirect('/sign-in');
  }

  const projects = await getProjects();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Projects</h1>
          <p className="text-gray-600">
            Manage your construction projects and track progress
          </p>
        </div>
        <Link href="/dashboard/projects/new">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            New Project
          </Button>
        </Link>
      </div>

      {projects.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <div className="text-center space-y-4">
              <div className="text-4xl">📋</div>
              <h3 className="text-lg font-medium">No projects yet</h3>
              <p className="text-gray-500 max-w-md">
                Get started by creating your first construction project. 
                You'll be able to assign cranes and schedule bookings.
              </p>
              <Link href="/dashboard/projects/new">
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Your First Project
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {projects.map((project) => (
            <ProjectCard key={project.id} project={project} />
          ))}
        </div>
      )}
    </div>
  );
}
