import { currentUser } from '@clerk/nextjs/server';
import { redirect, notFound } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ArrowLeft, 
  Edit, 
  Wrench, 
  Calendar, 
  Ruler, 
  Weight,
  Activity,
  MapPin,
  Clock
} from 'lucide-react';

// This would typically come from an API call
async function getCrane(id: string) {
  // TODO: Replace with actual API call to /api/cranes/[id]
  const cranes = [
    {
      id: '1',
      name: 'Tower Crane TC-001',
      model: 'Liebherr 280 EC-H',
      type: 'tower',
      capacity: '12.0',
      height: '60.0',
      reach: '70.0',
      status: 'available',
      lastMaintenanceDate: '2024-01-15',
      nextMaintenanceDate: '2024-04-15',
      createdAt: '2024-01-01',
      metadata: {
        serialNumber: 'LH280-2024-001',
        yearManufactured: '2023',
        certificationExpiry: '2025-01-15'
      }
    },
    {
      id: '2',
      name: 'Mobile Crane MC-002',
      model: 'Tadano GR-1000XL',
      type: 'mobile',
      capacity: '100.0',
      height: '45.0',
      reach: '52.0',
      status: 'in-use',
      lastMaintenanceDate: '2024-02-01',
      nextMaintenanceDate: '2024-05-01',
      createdAt: '2024-01-10',
      metadata: {
        serialNumber: 'TD1000-2024-002',
        yearManufactured: '2022',
        certificationExpiry: '2025-02-01'
      }
    },
  ];
  
  return cranes.find(c => c.id === id);
}

function getStatusColor(status: string) {
  switch (status) {
    case 'available':
      return 'bg-green-100 text-green-800';
    case 'in-use':
      return 'bg-blue-100 text-blue-800';
    case 'maintenance':
      return 'bg-yellow-100 text-yellow-800';
    case 'out-of-service':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

function getTypeIcon(type: string) {
  switch (type) {
    case 'tower':
      return '🏗️';
    case 'mobile':
      return '🚛';
    case 'crawler':
      return '🚜';
    default:
      return '🏗️';
  }
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-AU', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

function CraneOverview({ crane }: { crane: any }) {
  const isMaintenanceDue = crane.nextMaintenanceDate && 
    new Date(crane.nextMaintenanceDate) <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Crane Specifications</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Model</label>
                <p className="mt-1 font-medium">{crane.model}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Type</label>
                <div className="flex items-center mt-1">
                  <span className="text-xl mr-2">{getTypeIcon(crane.type)}</span>
                  <span className="capitalize">{crane.type} crane</span>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-500">Serial Number</label>
                <p className="mt-1">{crane.metadata?.serialNumber || 'Not specified'}</p>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Capacity</label>
                  <div className="flex items-center mt-1">
                    <Weight className="h-4 w-4 mr-1 text-gray-400" />
                    <span className="font-medium">{crane.capacity}t</span>
                  </div>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-gray-500">Height</label>
                  <div className="flex items-center mt-1">
                    <Ruler className="h-4 w-4 mr-1 text-gray-400" />
                    <span className="font-medium">{crane.height}m</span>
                  </div>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-gray-500">Reach</label>
                  <div className="flex items-center mt-1">
                    <Ruler className="h-4 w-4 mr-1 text-gray-400" />
                    <span className="font-medium">{crane.reach}m</span>
                  </div>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-500">Year Manufactured</label>
                <p className="mt-1">{crane.metadata?.yearManufactured || 'Not specified'}</p>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-500">Certification Expiry</label>
                <p className="mt-1">{crane.metadata?.certificationExpiry ? formatDate(crane.metadata.certificationExpiry) : 'Not specified'}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Maintenance Schedule</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="text-sm font-medium text-gray-500">Last Maintenance</label>
              <div className="flex items-center mt-1">
                <Wrench className="h-4 w-4 mr-2 text-gray-400" />
                <span>{formatDate(crane.lastMaintenanceDate)}</span>
              </div>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-500">Next Maintenance</label>
              <div className="flex items-center mt-1">
                <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                <span>{formatDate(crane.nextMaintenanceDate)}</span>
                {isMaintenanceDue && (
                  <Badge variant="outline" className="ml-2 text-orange-600 border-orange-600">
                    Due Soon
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Calendar className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Active Bookings</p>
                <p className="text-2xl font-bold">5</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Hours This Month</p>
                <p className="text-2xl font-bold">156</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <MapPin className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Current Location</p>
                <p className="text-sm font-bold">Project Site A</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

function CraneBookings() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Booking Schedule</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-gray-500">Booking calendar interface will be implemented here.</p>
      </CardContent>
    </Card>
  );
}

function CraneMaintenance() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Maintenance History</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-gray-500">Maintenance history and scheduling will be implemented here.</p>
      </CardContent>
    </Card>
  );
}

function CraneActivity() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-start space-x-3">
            <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
            <div>
              <p className="text-sm font-medium">Crane assigned to Project Downtown Office</p>
              <p className="text-xs text-gray-500">2 hours ago</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <div className="w-2 h-2 bg-green-600 rounded-full mt-2"></div>
            <div>
              <p className="text-sm font-medium">Maintenance completed</p>
              <p className="text-xs text-gray-500">3 days ago</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <div className="w-2 h-2 bg-yellow-600 rounded-full mt-2"></div>
            <div>
              <p className="text-sm font-medium">Status changed to Available</p>
              <p className="text-xs text-gray-500">1 week ago</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default async function CraneDetailPage({ 
  params 
}: { 
  params: { id: string } 
}) {
  const user = await currentUser();
  
  if (!user) {
    redirect('/sign-in');
  }

  const crane = await getCrane(params.id);
  
  if (!crane) {
    notFound();
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/dashboard/cranes">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Fleet
            </Button>
          </Link>
          <div>
            <div className="flex items-center gap-3">
              <span className="text-3xl">{getTypeIcon(crane.type)}</span>
              <h1 className="text-2xl font-bold">{crane.name}</h1>
              <Badge className={getStatusColor(crane.status)}>
                {crane.status.charAt(0).toUpperCase() + crane.status.slice(1).replace('-', ' ')}
              </Badge>
            </div>
            <p className="text-gray-600">
              Added on {formatDate(crane.createdAt)}
            </p>
          </div>
        </div>
        <Link href={`/dashboard/cranes/${crane.id}/edit`}>
          <Button>
            <Edit className="h-4 w-4 mr-2" />
            Edit Crane
          </Button>
        </Link>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="bookings">Bookings</TabsTrigger>
          <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview">
          <CraneOverview crane={crane} />
        </TabsContent>
        
        <TabsContent value="bookings">
          <CraneBookings />
        </TabsContent>
        
        <TabsContent value="maintenance">
          <CraneMaintenance />
        </TabsContent>
        
        <TabsContent value="activity">
          <CraneActivity />
        </TabsContent>
      </Tabs>
    </div>
  );
}
