import { currentUser } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Wrench, Calendar, Ruler, Weight, Plus, Eye } from 'lucide-react';

// This would typically come from an API call
async function getCranes() {
  // TODO: Replace with actual API call to /api/cranes
  return [
    {
      id: '1',
      name: 'Tower Crane TC-001',
      model: 'Liebherr 280 EC-H',
      type: 'tower',
      capacity: '12.0',
      height: '60.0',
      reach: '70.0',
      status: 'available',
      lastMaintenanceDate: '2024-01-15',
      nextMaintenanceDate: '2024-04-15',
      createdAt: '2024-01-01',
    },
    {
      id: '2',
      name: 'Mobile Crane MC-002',
      model: 'Tadano GR-1000XL',
      type: 'mobile',
      capacity: '100.0',
      height: '45.0',
      reach: '52.0',
      status: 'in-use',
      lastMaintenanceDate: '2024-02-01',
      nextMaintenanceDate: '2024-05-01',
      createdAt: '2024-01-10',
    },
    {
      id: '3',
      name: 'Crawler Crane CC-003',
      model: 'Manitowoc 18000',
      type: 'crawler',
      capacity: '450.0',
      height: '80.0',
      reach: '85.0',
      status: 'maintenance',
      lastMaintenanceDate: '2024-03-01',
      nextMaintenanceDate: '2024-03-15',
      createdAt: '2024-02-01',
    },
  ];
}

function getStatusColor(status: string) {
  switch (status) {
    case 'available':
      return 'bg-green-100 text-green-800';
    case 'in-use':
      return 'bg-blue-100 text-blue-800';
    case 'maintenance':
      return 'bg-yellow-100 text-yellow-800';
    case 'out-of-service':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

function getTypeIcon(type: string) {
  switch (type) {
    case 'tower':
      return '🏗️';
    case 'mobile':
      return '🚛';
    case 'crawler':
      return '🚜';
    default:
      return '🏗️';
  }
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-AU', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
}

function CraneCard({ crane }: { crane: any }) {
  const isMaintenanceDue = crane.nextMaintenanceDate && 
    new Date(crane.nextMaintenanceDate) <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="space-y-1">
            <CardTitle className="text-lg flex items-center gap-2">
              <span className="text-2xl">{getTypeIcon(crane.type)}</span>
              <Link 
                href={`/dashboard/cranes/${crane.id}`}
                className="hover:text-blue-600 transition-colors"
              >
                {crane.name}
              </Link>
            </CardTitle>
            <div className="flex items-center gap-2">
              <Badge className={getStatusColor(crane.status)}>
                {crane.status.charAt(0).toUpperCase() + crane.status.slice(1).replace('-', ' ')}
              </Badge>
              {isMaintenanceDue && (
                <Badge variant="outline" className="text-orange-600 border-orange-600">
                  Maintenance Due
                </Badge>
              )}
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-sm text-gray-600">
          <p className="font-medium">{crane.model}</p>
          <p className="capitalize">{crane.type} crane</p>
        </div>
        
        <div className="grid grid-cols-3 gap-4 text-sm">
          <div className="flex items-center">
            <Weight className="h-4 w-4 mr-1 text-gray-400" />
            <div>
              <p className="text-xs text-gray-500">Capacity</p>
              <p className="font-medium">{crane.capacity}t</p>
            </div>
          </div>
          
          <div className="flex items-center">
            <Ruler className="h-4 w-4 mr-1 text-gray-400" />
            <div>
              <p className="text-xs text-gray-500">Height</p>
              <p className="font-medium">{crane.height}m</p>
            </div>
          </div>
          
          <div className="flex items-center">
            <Ruler className="h-4 w-4 mr-1 text-gray-400" />
            <div>
              <p className="text-xs text-gray-500">Reach</p>
              <p className="font-medium">{crane.reach}m</p>
            </div>
          </div>
        </div>
        
        <div className="space-y-2">
          <div className="flex items-center text-sm text-gray-500">
            <Wrench className="h-4 w-4 mr-2" />
            Last maintenance: {formatDate(crane.lastMaintenanceDate)}
          </div>
          
          <div className="flex items-center text-sm text-gray-500">
            <Calendar className="h-4 w-4 mr-2" />
            Next maintenance: {formatDate(crane.nextMaintenanceDate)}
          </div>
        </div>
        
        <div className="flex justify-between items-center pt-2">
          <span className="text-xs text-gray-400">
            Added {formatDate(crane.createdAt)}
          </span>
          <Link href={`/dashboard/cranes/${crane.id}`}>
            <Button variant="outline" size="sm">
              <Eye className="h-4 w-4 mr-1" />
              View Details
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}

export default async function CranesPage() {
  const user = await currentUser();
  
  if (!user) {
    redirect('/sign-in');
  }

  const cranes = await getCranes();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Crane Fleet</h1>
          <p className="text-gray-600">
            Manage your crane fleet and track maintenance schedules
          </p>
        </div>
        <Link href="/dashboard/cranes/new">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add Crane
          </Button>
        </Link>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <div className="w-4 h-4 bg-green-600 rounded-full"></div>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Available</p>
                <p className="text-lg font-bold">{cranes.filter(c => c.status === 'available').length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <div className="w-4 h-4 bg-blue-600 rounded-full"></div>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">In Use</p>
                <p className="text-lg font-bold">{cranes.filter(c => c.status === 'in-use').length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <div className="w-4 h-4 bg-yellow-600 rounded-full"></div>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Maintenance</p>
                <p className="text-lg font-bold">{cranes.filter(c => c.status === 'maintenance').length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-gray-100 rounded-lg">
                <div className="w-4 h-4 bg-gray-600 rounded-full"></div>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Total Fleet</p>
                <p className="text-lg font-bold">{cranes.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {cranes.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <div className="text-center space-y-4">
              <div className="text-4xl">🏗️</div>
              <h3 className="text-lg font-medium">No cranes in your fleet</h3>
              <p className="text-gray-500 max-w-md">
                Start building your crane fleet by adding your first crane. 
                You'll be able to track maintenance and assign them to projects.
              </p>
              <Link href="/dashboard/cranes/new">
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Your First Crane
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {cranes.map((crane) => (
            <CraneCard key={crane.id} crane={crane} />
          ))}
        </div>
      )}
    </div>
  );
}
