import { currentUser } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import DashboardWelcome from './dashboard-welcome';

export default async function DashboardPage() {
  const user = await currentUser();
  
  // If no user is logged in, redirect to sign-in
  if (!user) {
    redirect('/sign-in');
  }

  // Get user role from public metadata
  const userRole = user.publicMetadata.role as string || 'member';
  const companyName = user.publicMetadata.companyName as string || '';

  return (
    <div className="container mx-auto px-4 py-8">
      <DashboardWelcome user={user} role={userRole} companyName={companyName} />
      
      <div className="mt-8 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Dashboard cards will be added here based on user role */}
        <DashboardCard
          title="Projects"
          description="Manage your construction projects"
          icon="📋"
          href="/dashboard/projects"
        />
        
        <DashboardCard
          title="Cranes"
          description="View and manage your crane fleet"
          icon="🏗️"
          href="/dashboard/cranes"
        />
        
        <DashboardCard
          title="Schedule"
          description="View and manage crane bookings"
          icon="📅"
          href="/dashboard/schedule"
        />
        
        {userRole === 'admin' && (
          <DashboardCard
            title="Team"
            description="Manage your team members"
            icon="👥"
            href="/dashboard/team"
          />
        )}
        
        {(userRole === 'admin' || userRole === 'manager') && (
          <DashboardCard
            title="Analytics"
            description="View usage reports and statistics"
            icon="📊"
            href="/dashboard/analytics"
          />
        )}
      </div>
    </div>
  );
}

type DashboardCardProps = {
  title: string;
  description: string;
  icon: string;
  href: string;
};

function DashboardCard({ title, description, icon, href }: DashboardCardProps) {
  return (
    <a
      href={href}
      className="flex flex-col rounded-lg border bg-white p-6 shadow-sm transition-all hover:shadow-md"
    >
      <div className="mb-4 text-4xl">{icon}</div>
      <h3 className="mb-2 text-lg font-medium">{title}</h3>
      <p className="text-sm text-gray-600">{description}</p>
    </a>
  );
}
