import './globals.css';
import type { Metada<PERSON>, Viewport } from 'next';
import { Manrope } from 'next/font/google';
import { SWRConfig } from 'swr';
import { ClerkProvider } from '@clerk/nextjs';

export const metadata: Metadata = {
  title: 'LiftrUp',
  description: 'Equipment management platform for construction companies.'
};

export const viewport: Viewport = {
  maximumScale: 1
};

const manrope = Manrope({ subsets: ['latin'] });

export default function RootLayout({
  children
}: {
  children: React.ReactNode;
}) {
  return (
    <ClerkProvider>
      <html
        lang="en"
        className={`bg-white dark:bg-gray-950 text-black dark:text-white ${manrope.className}`}
      >
        <body className="min-h-[100dvh] bg-gray-50">
          <SWRConfig
            value={{
              fallback: {
                // Clerk will handle user data now
              }
            }}
          >
            {children}
          </SWRConfig>
        </body>
      </html>
    </ClerkProvider>
  );
}
