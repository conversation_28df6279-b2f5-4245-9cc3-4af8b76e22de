'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import type { User } from '@clerk/nextjs/server';

// Define the role options based on project requirements
const ROLE_OPTIONS = [
  { value: 'admin', label: 'Administrator' },
  { value: 'manager', label: 'Project Manager' },
  { value: 'operator', label: 'Crane Operator' },
  { value: 'subcontractor', label: 'Subcontractor' },
];

type OnboardingFormProps = {
  user: User;
};

export default function OnboardingForm({ user }: OnboardingFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Form state
  const [role, setRole] = useState('');
  const [companyName, setCompanyName] = useState('');
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    
    try {
      const response = await fetch('/api/users/onboarding', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          role,
          companyName,
        }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Something went wrong');
      }
      
      // Redirect to dashboard after successful onboarding
      router.push('/dashboard');
      router.refresh(); // Refresh to update the user metadata in the UI
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <div className="max-w-md w-full mx-auto p-8 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6 text-center">Complete Your Profile</h2>
      
      <div className="mb-6 text-center">
        <p className="text-gray-600">
          Welcome, {user.firstName || user.emailAddresses[0].emailAddress.split('@')[0]}!
        </p>
        <p className="text-sm text-gray-500 mt-1">
          Please provide the following information to complete your account setup.
        </p>
      </div>
      
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-md">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label className="block mb-3 font-medium text-gray-700">Select Your Role</label>
          <div className="space-y-3 bg-gray-50 p-4 rounded-md">
            {ROLE_OPTIONS.map((option) => (
              <label key={option.value} className="flex items-center cursor-pointer">
                <input
                  type="radio"
                  name="role"
                  value={option.value}
                  checked={role === option.value}
                  onChange={(e) => setRole(e.target.value)}
                  className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500"
                  required
                />
                <span>{option.label}</span>
              </label>
            ))}
          </div>
        </div>
        
        <div>
          <label htmlFor="companyName" className="block mb-2 font-medium text-gray-700">
            Company Name
          </label>
          <input
            type="text"
            id="companyName"
            value={companyName}
            onChange={(e) => setCompanyName(e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter your company name"
            required
          />
        </div>
        
        <button
          type="submit"
          disabled={isSubmitting}
          className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 font-medium transition-colors"
        >
          {isSubmitting ? 'Submitting...' : 'Complete Onboarding'}
        </button>
      </form>
    </div>
  );
}
