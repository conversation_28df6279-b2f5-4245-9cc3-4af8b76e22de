import Link from 'next/link';
import Image from 'next/image';
import { auth } from '@clerk/nextjs/server';

export default async function HomePage() {
  const { userId } = await auth();
  const isSignedIn = !!userId;

  return (
    <div className="flex min-h-screen flex-col">
      {/* Header */}
      <header className="flex h-16 items-center justify-between border-b px-4 md:px-6">
        <Link href="/" className="flex items-center gap-2">
          <Image
            src="/logo.svg"
            alt="LiftrUP Logo"
            width={32}
            height={32}
            className="h-8 w-8"
          />
          <span className="text-xl font-bold">LiftrUP</span>
        </Link>
        
        <div className="flex items-center gap-4">
          {isSignedIn ? (
            <Link
              href="/dashboard"
              className="rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-blue-700"
            >
              Dashboard
            </Link>
          ) : (
            <>
              <Link
                href="/sign-in"
                className="text-sm font-medium text-gray-700 transition-colors hover:text-blue-600"
              >
                Sign In
              </Link>
              <Link
                href="/sign-up"
                className="rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-blue-700"
              >
                Sign Up
              </Link>
            </>
          )}
        </div>
      </header>
      
      {/* Hero Section */}
      <section className="flex flex-1 flex-col items-center justify-center px-4 py-12 text-center md:py-24">
        <h1 className="mb-6 text-4xl font-bold tracking-tight md:text-6xl">
          Streamline Your Crane Scheduling
        </h1>
        <p className="mb-8 max-w-2xl text-lg text-gray-600">
          LiftrUP helps construction teams eliminate scheduling conflicts and downtime with real-time collaborative scheduling and centralized project oversight.
        </p>
        <div className="flex flex-col gap-4 sm:flex-row">
          {isSignedIn ? (
            <Link
              href="/dashboard"
              className="rounded-md bg-blue-600 px-6 py-3 text-base font-medium text-white transition-colors hover:bg-blue-700"
            >
              Go to Dashboard
            </Link>
          ) : (
            <>
              <Link
                href="/sign-up"
                className="rounded-md bg-blue-600 px-6 py-3 text-base font-medium text-white transition-colors hover:bg-blue-700"
              >
                Get Started
              </Link>
              <Link
                href="/about"
                className="rounded-md border border-gray-300 bg-white px-6 py-3 text-base font-medium text-gray-700 transition-colors hover:bg-gray-50"
              >
                Learn More
              </Link>
            </>
          )}
        </div>
      </section>
      
      {/* Features Section */}
      <section className="border-t bg-gray-50 px-4 py-12 md:py-24">
        <div className="container mx-auto">
          <h2 className="mb-12 text-center text-3xl font-bold">Key Features</h2>
          
          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            <div className="rounded-lg bg-white p-6 shadow-sm">
              <div className="mb-4 text-3xl">📅</div>
              <h3 className="mb-2 text-xl font-medium">Real-time Scheduling</h3>
              <p className="text-gray-600">
                Collaborative scheduling with conflict detection to eliminate double-bookings and downtime.
              </p>
            </div>
            
            <div className="rounded-lg bg-white p-6 shadow-sm">
              <div className="mb-4 text-3xl">👥</div>
              <h3 className="mb-2 text-xl font-medium">Role-based Access</h3>
              <p className="text-gray-600">
                Customized permissions for project managers, operators, and subcontractors.
              </p>
            </div>
            
            <div className="rounded-lg bg-white p-6 shadow-sm">
              <div className="mb-4 text-3xl">📊</div>
              <h3 className="mb-2 text-xl font-medium">Analytics Dashboard</h3>
              <p className="text-gray-600">
                Comprehensive reporting on crane utilization, project timelines, and team performance.
              </p>
            </div>
          </div>
        </div>
      </section>
      
      {/* Footer */}
      <footer className="border-t py-8">
        <div className="container mx-auto px-4 text-center">
          <p className="text-sm text-gray-600">
            © {new Date().getFullYear()} LiftrUP. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
}
