import { Webhook } from 'svix';
import { headers } from 'next/headers';
import { WebhookEvent } from '@clerk/nextjs/server';
import { db } from '@/lib/db';
import { users } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export async function POST(req: Request) {
  // Get the webhook signature from the headers
  const headersList = headers();
  const svix_id = headersList.get('svix-id');
  const svix_timestamp = headersList.get('svix-timestamp');
  const svix_signature = headersList.get('svix-signature');
  
  // If there are no headers, error out
  if (!svix_id || !svix_timestamp || !svix_signature) {
    return new Response('Missing svix headers', { status: 400 });
  }
  
  // Get the body
  const payload = await req.json();
  const body = JSON.stringify(payload);
  
  // Create a new Svix instance with your webhook secret
  const webhookSecret = process.env.CLERK_WEBHOOK_SECRET;
  
  if (!webhookSecret) {
    return new Response('Missing webhook secret', { status: 500 });
  }
  
  // Create a new Svix instance with your webhook secret
  const wh = new Webhook(webhookSecret);
  
  let evt: WebhookEvent;
  
  // Verify the webhook
  try {
    evt = wh.verify(body, {
      'svix-id': svix_id,
      'svix-timestamp': svix_timestamp,
      'svix-signature': svix_signature,
    }) as WebhookEvent;
  } catch (err) {
    console.error('Error verifying webhook:', err);
    return new Response('Error verifying webhook', { status: 400 });
  }
  
  // Handle the webhook event
  const eventType = evt.type;
  
  if (eventType === 'user.created') {
    const { id, email_addresses, first_name, last_name } = evt.data;
    
    // Create a new user in our database
    try {
      await db.insert(users).values({
        clerkId: id,
        email: email_addresses[0].email_address,
        name: `${first_name || ''} ${last_name || ''}`.trim(),
        role: 'member', // Default role
        createdAt: new Date(),
        updatedAt: new Date(),
      });
      
      return new Response('User created', { status: 201 });
    } catch (error) {
      console.error('Error creating user:', error);
      return new Response('Error creating user', { status: 500 });
    }
  }
  
  if (eventType === 'user.updated') {
    const { id, email_addresses, first_name, last_name, public_metadata } = evt.data;
    
    try {
      // Check if user exists in our database
      const existingUser = await db.query.users.findFirst({
        where: eq(users.clerkId, id),
      });
      
      if (existingUser) {
        // Update existing user
        await db.update(users)
          .set({
            email: email_addresses[0].email_address,
            name: `${first_name || ''} ${last_name || ''}`.trim(),
            role: (public_metadata.role as string) || existingUser.role,
            companyName: (public_metadata.companyName as string) || existingUser.companyName,
            updatedAt: new Date(),
          })
          .where(eq(users.clerkId, id));
      } else {
        // Create new user if they don't exist
        await db.insert(users).values({
          clerkId: id,
          email: email_addresses[0].email_address,
          name: `${first_name || ''} ${last_name || ''}`.trim(),
          role: (public_metadata.role as string) || 'member',
          companyName: (public_metadata.companyName as string) || '',
          createdAt: new Date(),
          updatedAt: new Date(),
        });
      }
      
      return new Response('User updated', { status: 200 });
    } catch (error) {
      console.error('Error updating user:', error);
      return new Response('Error updating user', { status: 500 });
    }
  }
  
  if (eventType === 'user.deleted') {
    const { id } = evt.data;
    
    try {
      // Soft delete the user in our database
      await db.update(users)
        .set({
          deletedAt: new Date(),
        })
        .where(eq(users.clerkId, id as string));
      
      return new Response('User deleted', { status: 200 });
    } catch (error) {
      console.error('Error deleting user:', error);
      return new Response('Error deleting user', { status: 500 });
    }
  }
  
  // Return a 200 response for other event types
  return new Response('Webhook received', { status: 200 });
}
