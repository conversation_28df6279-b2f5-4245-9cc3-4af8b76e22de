import { NextRequest, NextResponse } from "next/server";
import { auth, currentUser } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { bookings, cranes, teams, teamMembers, users } from "@/lib/db/schema";
import { eq, and, isNull, between, or } from "drizzle-orm";

// POST /api/bookings/availability - Check availability for a crane during a time period
export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();
    const user = await currentUser();

    if (!userId || !user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const body = await req.json();
    const { craneId, startDate, endDate, excludeBookingId } = body;

    if (!craneId) {
      return new NextResponse("Crane ID is required", { status: 400 });
    }

    if (!startDate || !endDate) {
      return new NextResponse("Start and end dates are required", { status: 400 });
    }

    // Get the user from the database
    const dbUser = await db.query.users.findFirst({
      where: eq(users.clerkId, userId),
    });

    if (!dbUser) {
      return new NextResponse("User not found", { status: 404 });
    }

    // Verify the crane exists and is not deleted
    const crane = await db.query.cranes.findFirst({
      where: and(eq(cranes.id, craneId), isNull(cranes.deletedAt)),
    });

    if (!crane) {
      return new NextResponse("Crane not found", { status: 404 });
    }

    // Verify the user is a member of the team that owns the crane
    const teamMembership = await db.query.teamMembers.findFirst({
      where: and(
        eq(teamMembers.userId, dbUser.id),
        eq(teamMembers.teamId, crane.teamId)
      ),
    });

    if (!teamMembership) {
      return new NextResponse("Access denied", { status: 403 });
    }

    // Check for booking conflicts
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);
    
    let conflictQuery = and(
      eq(bookings.craneId, craneId),
      isNull(bookings.deletedAt),
      or(
        // Case 1: New booking starts or ends during an existing booking
        and(
          between(startDateObj, bookings.startDate, bookings.endDate),
          between(endDateObj, bookings.startDate, bookings.endDate)
        ),
        // Case 2: New booking completely contains an existing booking
        and(
          startDateObj.getTime() <= bookings.startDate.getTime(),
          endDateObj.getTime() >= bookings.endDate.getTime()
        ),
        // Case 3: New booking is completely contained within an existing booking
        and(
          startDateObj.getTime() >= bookings.startDate.getTime(),
          endDateObj.getTime() <= bookings.endDate.getTime()
        ),
        // Case 4: New booking starts before and ends during an existing booking
        and(
          startDateObj.getTime() < bookings.startDate.getTime(),
          endDateObj.getTime() > bookings.startDate.getTime(),
          endDateObj.getTime() <= bookings.endDate.getTime()
        ),
        // Case 5: New booking starts during and ends after an existing booking
        and(
          startDateObj.getTime() >= bookings.startDate.getTime(),
          startDateObj.getTime() < bookings.endDate.getTime(),
          endDateObj.getTime() > bookings.endDate.getTime()
        )
      )
    );

    // If we're checking for an existing booking (to update it), exclude that booking from the conflict check
    if (excludeBookingId) {
      conflictQuery = and(conflictQuery, eq(bookings.id, excludeBookingId).not());
    }

    const conflictingBookings = await db.query.bookings.findMany({
      where: conflictQuery,
      with: {
        project: true,
      },
    });

    if (conflictingBookings.length > 0) {
      return NextResponse.json({
        available: false,
        conflicts: conflictingBookings.map(booking => ({
          id: booking.id,
          projectName: booking.project.name,
          startDate: booking.startDate,
          endDate: booking.endDate,
          status: booking.status
        }))
      });
    }

    return NextResponse.json({
      available: true,
      conflicts: []
    });
  } catch (error) {
    console.error("[BOOKINGS_AVAILABILITY]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}
