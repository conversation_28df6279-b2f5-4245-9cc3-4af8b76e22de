import { NextRequest, NextResponse } from "next/server";
import { auth, currentUser } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { bookings, cranes, projects, teams, teamMembers, users, activityLogs, ActivityType } from "@/lib/db/schema";
import { eq, and, isNull, between, or } from "drizzle-orm";

// GET /api/bookings/[bookingId] - Get a specific booking by ID
export async function GET(
  req: NextRequest,
  { params }: { params: { bookingId: string } }
) {
  try {
    const { userId } = await auth();
    const user = await currentUser();

    if (!userId || !user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const bookingId = parseInt(params.bookingId);
    if (isNaN(bookingId)) {
      return new NextResponse("Invalid booking ID", { status: 400 });
    }

    // Get the user from the database
    const dbUser = await db.query.users.findFirst({
      where: eq(users.clerkId, userId),
    });

    if (!dbUser) {
      return new NextResponse("User not found", { status: 404 });
    }

    // Get the booking
    const booking = await db.query.bookings.findFirst({
      where: and(eq(bookings.id, bookingId), isNull(bookings.deletedAt)),
      with: {
        project: true,
        crane: true,
        createdBy: {
          columns: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!booking) {
      return new NextResponse("Booking not found", { status: 404 });
    }

    // Verify the user is a member of the team that owns the project
    const project = booking.project;
    const teamMembership = await db.query.teamMembers.findFirst({
      where: and(
        eq(teamMembers.userId, dbUser.id),
        eq(teamMembers.teamId, project.teamId)
      ),
    });

    if (!teamMembership) {
      return new NextResponse("Access denied", { status: 403 });
    }

    return NextResponse.json(booking);
  } catch (error) {
    console.error("[BOOKING_GET]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

// PUT /api/bookings/[bookingId] - Update a specific booking
export async function PUT(
  req: NextRequest,
  { params }: { params: { bookingId: string } }
) {
  try {
    const { userId } = await auth();
    const user = await currentUser();

    if (!userId || !user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const bookingId = parseInt(params.bookingId);
    if (isNaN(bookingId)) {
      return new NextResponse("Invalid booking ID", { status: 400 });
    }

    const body = await req.json();
    const { craneId, projectId, startDate, endDate, notes, status } = body;

    // Get the user from the database
    const dbUser = await db.query.users.findFirst({
      where: eq(users.clerkId, userId),
    });

    if (!dbUser) {
      return new NextResponse("User not found", { status: 404 });
    }

    // Get the booking
    const booking = await db.query.bookings.findFirst({
      where: and(eq(bookings.id, bookingId), isNull(bookings.deletedAt)),
      with: {
        project: true,
        crane: true,
      },
    });

    if (!booking) {
      return new NextResponse("Booking not found", { status: 404 });
    }

    // Verify the user is a member of the team that owns the project
    const teamMembership = await db.query.teamMembers.findFirst({
      where: and(
        eq(teamMembers.userId, dbUser.id),
        eq(teamMembers.teamId, booking.project.teamId)
      ),
    });

    if (!teamMembership) {
      return new NextResponse("Access denied", { status: 403 });
    }

    // If changing crane or dates, check for conflicts
    if ((craneId && craneId !== booking.craneId) || 
        (startDate && startDate !== booking.startDate.toISOString()) || 
        (endDate && endDate !== booking.endDate.toISOString())) {
      
      const finalCraneId = craneId || booking.craneId;
      const startDateObj = startDate ? new Date(startDate) : booking.startDate;
      const endDateObj = endDate ? new Date(endDate) : booking.endDate;
      
      // Check for booking conflicts
      const conflictingBookings = await db.query.bookings.findMany({
        where: and(
          eq(bookings.craneId, finalCraneId),
          isNull(bookings.deletedAt),
          eq(bookings.id, bookingId).not(), // Exclude current booking
          or(
            and(
              between(bookings.startDate, startDateObj, endDateObj),
              between(bookings.endDate, startDateObj, endDateObj)
            ),
            and(
              between(startDateObj, bookings.startDate, bookings.endDate),
              between(endDateObj, bookings.startDate, bookings.endDate)
            )
          )
        ),
      });

      if (conflictingBookings.length > 0) {
        return new NextResponse("Booking conflicts with existing bookings", { status: 409 });
      }
    }

    // If changing project, verify it belongs to the same team
    if (projectId && projectId !== booking.projectId) {
      const newProject = await db.query.projects.findFirst({
        where: and(eq(projects.id, projectId), isNull(projects.deletedAt)),
      });

      if (!newProject) {
        return new NextResponse("Project not found", { status: 404 });
      }

      if (newProject.teamId !== booking.project.teamId) {
        return new NextResponse("Project must belong to the same team", { status: 400 });
      }
    }

    // If changing crane, verify it belongs to the same team
    if (craneId && craneId !== booking.craneId) {
      const newCrane = await db.query.cranes.findFirst({
        where: and(eq(cranes.id, craneId), isNull(cranes.deletedAt)),
      });

      if (!newCrane) {
        return new NextResponse("Crane not found", { status: 404 });
      }

      if (newCrane.teamId !== booking.project.teamId) {
        return new NextResponse("Crane must belong to the same team", { status: 400 });
      }
    }

    // Update the booking
    const [updatedBooking] = await db
      .update(bookings)
      .set({
        craneId: craneId || booking.craneId,
        projectId: projectId || booking.projectId,
        startDate: startDate ? new Date(startDate) : booking.startDate,
        endDate: endDate ? new Date(endDate) : booking.endDate,
        notes: notes !== undefined ? notes : booking.notes,
        status: status || booking.status,
        updatedAt: new Date(),
      })
      .where(eq(bookings.id, bookingId))
      .returning();

    // Log the activity
    await db.insert(activityLogs).values({
      teamId: booking.project.teamId,
      userId: dbUser.id,
      action: ActivityType.UPDATE_BOOKING,
      ipAddress: req.headers.get("x-forwarded-for") || undefined,
    });

    return NextResponse.json(updatedBooking);
  } catch (error) {
    console.error("[BOOKING_PUT]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

// DELETE /api/bookings/[bookingId] - Soft delete a specific booking
export async function DELETE(
  req: NextRequest,
  { params }: { params: { bookingId: string } }
) {
  try {
    const { userId } = await auth();
    const user = await currentUser();

    if (!userId || !user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const bookingId = parseInt(params.bookingId);
    if (isNaN(bookingId)) {
      return new NextResponse("Invalid booking ID", { status: 400 });
    }

    // Get the user from the database
    const dbUser = await db.query.users.findFirst({
      where: eq(users.clerkId, userId),
    });

    if (!dbUser) {
      return new NextResponse("User not found", { status: 404 });
    }

    // Get the booking
    const booking = await db.query.bookings.findFirst({
      where: and(eq(bookings.id, bookingId), isNull(bookings.deletedAt)),
      with: {
        project: true,
      },
    });

    if (!booking) {
      return new NextResponse("Booking not found", { status: 404 });
    }

    // Verify the user is a member of the team that owns the project with appropriate role
    const teamMembership = await db.query.teamMembers.findFirst({
      where: and(
        eq(teamMembers.userId, dbUser.id),
        eq(teamMembers.teamId, booking.project.teamId)
      ),
    });

    // Only allow creators, managers, admins, or owners to delete bookings
    const canDelete = 
      booking.createdById === dbUser.id || 
      (teamMembership && ["admin", "owner", "manager"].includes(teamMembership.role));

    if (!teamMembership || !canDelete) {
      return new NextResponse("Access denied", { status: 403 });
    }

    // Soft delete the booking
    await db
      .update(bookings)
      .set({
        deletedAt: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(bookings.id, bookingId));

    // Log the activity
    await db.insert(activityLogs).values({
      teamId: booking.project.teamId,
      userId: dbUser.id,
      action: ActivityType.DELETE_BOOKING,
      ipAddress: req.headers.get("x-forwarded-for") || undefined,
    });

    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error("[BOOKING_DELETE]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}
