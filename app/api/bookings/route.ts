import { NextRequest, NextResponse } from "next/server";
import { auth, currentUser } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { bookings, cranes, projects, organization, users, activityLogs, ActivityType } from "@/lib/db/schema";
import { eq, and, isNull, between, or, sql } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

// GET /api/bookings - Get all bookings
export async function GET(req: NextRequest) {
  try {
    const { userId } = await auth();
    const user = await currentUser();

    if (!userId || !user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Get the user from the database
    const dbUser = await db.query.users.findFirst({
      where: eq(users.clerkId, userId),
    });

    if (!dbUser) {
      return new NextResponse("User not found", { status: 404 });
    }
    
    // For now, we'll get all bookings regardless of organization
    // In a real implementation, you would check organization membership
    const userBookings = await db.query.bookings.findMany({
      where: isNull(bookings.deletedAt),
      with: {
        project: true,
        crane: true,
        createdBy: {
          columns: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: (booking) => [booking.startDate],
    });

    return NextResponse.json({ bookings: userBookings });
  } catch (error) {
    console.error("[BOOKINGS_GET]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

// POST /api/bookings - Create a new booking
export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();
    const user = await currentUser();

    if (!userId || !user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const body = await req.json();
    const { craneId, projectId, startDate, endDate, notes, status } = body;

    if (!craneId) {
      return new NextResponse("Crane ID is required", { status: 400 });
    }

    if (!projectId) {
      return new NextResponse("Project ID is required", { status: 400 });
    }

    if (!startDate || !endDate) {
      return new NextResponse("Start and end dates are required", { status: 400 });
    }

    // Get the user from the database
    const dbUser = await db.query.users.findFirst({
      where: eq(users.clerkId, userId),
    });

    if (!dbUser) {
      return new NextResponse("User not found", { status: 404 });
    }

    // Verify the crane exists and is not deleted
    const crane = await db.query.cranes.findFirst({
      where: and(eq(cranes.id, craneId), isNull(cranes.deletedAt)),
    });

    if (!crane) {
      return new NextResponse("Crane not found", { status: 404 });
    }

    // Verify the project exists and is not deleted
    const project = await db.query.projects.findFirst({
      where: and(eq(projects.id, projectId), isNull(projects.deletedAt)),
    });

    if (!project) {
      return new NextResponse("Project not found", { status: 404 });
    }

    // In a real implementation, you would check if the user has permission to create bookings for this project and crane
    // For now, we'll assume the user has access to all projects and cranes

    // Check for booking conflicts using SQL for proper date comparison
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);
    
    const conflictingBookings = await db.execute(sql`
      SELECT * FROM bookings 
      WHERE crane_id = ${craneId}
      AND deleted_at IS NULL
      AND (
        (start_date BETWEEN ${startDateObj} AND ${endDateObj})
        OR (end_date BETWEEN ${startDateObj} AND ${endDateObj})
        OR (${startDateObj} BETWEEN start_date AND end_date)
        OR (${endDateObj} BETWEEN start_date AND end_date)
      )
    `);

    if (conflictingBookings.length > 0) {
      return new NextResponse("Booking conflicts with existing bookings", { status: 409 });
    }

    // Create the booking with a UUID
    const bookingId = uuidv4();
    
    // Use raw SQL to insert with a specific ID
    const newBooking = await db.execute(sql`
      INSERT INTO bookings (
        id, crane_id, project_id, start_date, end_date, 
        created_by, notes, status, created_at, updated_at
      ) VALUES (
        ${bookingId}, ${craneId}, ${projectId}, ${startDateObj}, ${endDateObj},
        ${dbUser.id.toString()}, ${notes || null}, ${status || "scheduled"},
        ${new Date()}, ${new Date()}
      ) RETURNING *
    `);

    // Use raw SQL to insert activity log
    await db.execute(sql`
      INSERT INTO activity_logs (team_id, user_id, action, ip_address)
      VALUES (
        ${crane.teamId},
        ${dbUser.id},
        ${ActivityType.CREATE_BOOKING},
        ${req.headers.get("x-forwarded-for") || null}
      )
    `);

    return NextResponse.json(newBooking);
  } catch (error) {
    console.error("[BOOKINGS_POST]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}
