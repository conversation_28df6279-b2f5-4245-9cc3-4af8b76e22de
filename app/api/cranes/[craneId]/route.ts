import { NextRequest, NextResponse } from "next/server";
import { auth, currentUser } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { cranes, teams, teamMembers, users, activityLogs, ActivityType } from "@/lib/db/schema";
import { eq, and, isNull } from "drizzle-orm";

// GET /api/cranes/[craneId] - Get a specific crane by ID
export async function GET(
  req: NextRequest,
  { params }: { params: { craneId: string } }
) {
  try {
    const { userId } = await auth();
    const user = await currentUser();

    if (!userId || !user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const craneId = parseInt(params.craneId);
    if (isNaN(craneId)) {
      return new NextResponse("Invalid crane ID", { status: 400 });
    }

    // Get the user from the database
    const dbUser = await db.query.users.findFirst({
      where: eq(users.clerkId, userId),
    });

    if (!dbUser) {
      return new NextResponse("User not found", { status: 404 });
    }

    // Get the crane
    const crane = await db.query.cranes.findFirst({
      where: and(eq(cranes.id, craneId), isNull(cranes.deletedAt)),
      with: {
        team: true,
      },
    });

    if (!crane) {
      return new NextResponse("Crane not found", { status: 404 });
    }

    // Verify the user is a member of the team that owns the crane
    const teamMembership = await db.query.teamMembers.findFirst({
      where: and(
        eq(teamMembers.userId, dbUser.id),
        eq(teamMembers.teamId, crane.teamId)
      ),
    });

    if (!teamMembership) {
      return new NextResponse("Access denied", { status: 403 });
    }

    return NextResponse.json(crane);
  } catch (error) {
    console.error("[CRANE_GET]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

// PUT /api/cranes/[craneId] - Update a specific crane
export async function PUT(
  req: NextRequest,
  { params }: { params: { craneId: string } }
) {
  try {
    const { userId } = await auth();
    const user = await currentUser();

    if (!userId || !user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const craneId = parseInt(params.craneId);
    if (isNaN(craneId)) {
      return new NextResponse("Invalid crane ID", { status: 400 });
    }

    const body = await req.json();
    const { 
      name, 
      model, 
      type, 
      capacity, 
      height, 
      reach, 
      status,
      lastMaintenanceDate,
      nextMaintenanceDate
    } = body;

    // Get the user from the database
    const dbUser = await db.query.users.findFirst({
      where: eq(users.clerkId, userId),
    });

    if (!dbUser) {
      return new NextResponse("User not found", { status: 404 });
    }

    // Get the crane
    const crane = await db.query.cranes.findFirst({
      where: and(eq(cranes.id, craneId), isNull(cranes.deletedAt)),
    });

    if (!crane) {
      return new NextResponse("Crane not found", { status: 404 });
    }

    // Verify the user is a member of the team that owns the crane with appropriate role
    const teamMembership = await db.query.teamMembers.findFirst({
      where: and(
        eq(teamMembers.userId, dbUser.id),
        eq(teamMembers.teamId, crane.teamId)
      ),
    });

    if (!teamMembership || !["admin", "owner", "manager"].includes(teamMembership.role)) {
      return new NextResponse("Access denied", { status: 403 });
    }

    // Update the crane
    const [updatedCrane] = await db
      .update(cranes)
      .set({
        name: name || crane.name,
        model: model !== undefined ? model : crane.model,
        type: type || crane.type,
        capacity: capacity !== undefined ? capacity : crane.capacity,
        height: height !== undefined ? height : crane.height,
        reach: reach !== undefined ? reach : crane.reach,
        status: status || crane.status,
        lastMaintenanceDate: lastMaintenanceDate ? new Date(lastMaintenanceDate) : crane.lastMaintenanceDate,
        nextMaintenanceDate: nextMaintenanceDate ? new Date(nextMaintenanceDate) : crane.nextMaintenanceDate,
        updatedAt: new Date(),
      })
      .where(eq(cranes.id, craneId))
      .returning();

    // Log the activity
    await db.insert(activityLogs).values({
      teamId: crane.teamId,
      userId: dbUser.id,
      action: ActivityType.UPDATE_CRANE,
      ipAddress: req.headers.get("x-forwarded-for") || undefined,
    });

    return NextResponse.json(updatedCrane);
  } catch (error) {
    console.error("[CRANE_PUT]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

// DELETE /api/cranes/[craneId] - Soft delete a specific crane
export async function DELETE(
  req: NextRequest,
  { params }: { params: { craneId: string } }
) {
  try {
    const { userId } = await auth();
    const user = await currentUser();

    if (!userId || !user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const craneId = parseInt(params.craneId);
    if (isNaN(craneId)) {
      return new NextResponse("Invalid crane ID", { status: 400 });
    }

    // Get the user from the database
    const dbUser = await db.query.users.findFirst({
      where: eq(users.clerkId, userId),
    });

    if (!dbUser) {
      return new NextResponse("User not found", { status: 404 });
    }

    // Get the crane
    const crane = await db.query.cranes.findFirst({
      where: and(eq(cranes.id, craneId), isNull(cranes.deletedAt)),
    });

    if (!crane) {
      return new NextResponse("Crane not found", { status: 404 });
    }

    // Verify the user is a member of the team that owns the crane with appropriate role
    const teamMembership = await db.query.teamMembers.findFirst({
      where: and(
        eq(teamMembers.userId, dbUser.id),
        eq(teamMembers.teamId, crane.teamId)
      ),
    });

    if (!teamMembership || !["admin", "owner"].includes(teamMembership.role)) {
      return new NextResponse("Access denied", { status: 403 });
    }

    // Soft delete the crane
    await db
      .update(cranes)
      .set({
        deletedAt: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(cranes.id, craneId));

    // Log the activity
    await db.insert(activityLogs).values({
      teamId: crane.teamId,
      userId: dbUser.id,
      action: ActivityType.DELETE_CRANE,
      ipAddress: req.headers.get("x-forwarded-for") || undefined,
    });

    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error("[CRANE_DELETE]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}
