import { NextRequest, NextResponse } from "next/server";
import { auth, currentUser } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { cranes, teams, teamMembers, users } from "@/lib/db/schema";
import { eq, and, isNull, sql } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

// GET /api/cranes - Get all cranes for the current user's team
export async function GET(req: NextRequest) {
  try {
    const { userId } = await auth();
    const user = await currentUser();

    if (!userId || !user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Get the user from the database
    const dbUser = await db.query.users.findFirst({
      where: eq(users.clerkId, userId),
    });

    if (!dbUser) {
      return new NextResponse("User not found", { status: 404 });
    }

    // Get the user's team memberships
    const teamMemberships = await db.query.teamMembers.findMany({
      where: eq(teamMembers.userId, dbUser.id),
      with: {
        team: true,
      },
    });

    if (teamMemberships.length === 0) {
      return NextResponse.json({ cranes: [] });
    }

    // Get all cranes for the user's teams
    const teamIds = teamMemberships.map((membership) => membership.teamId);
    
    const userCranes = await db.query.cranes.findMany({
      where: (crane) => {
        const conditions = teamIds.map((teamId) => eq(crane.teamId, teamId));
        return and(...conditions, isNull(crane.deletedAt));
      },
      with: {
        team: true,
      },
      orderBy: (crane) => [crane.name],
    });

    return NextResponse.json({ cranes: userCranes });
  } catch (error) {
    console.error("[CRANES_GET]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

// POST /api/cranes - Create a new crane
export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();
    const user = await currentUser();

    if (!userId || !user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const body = await req.json();
    const { 
      name, 
      model, 
      type, 
      capacity, 
      height, 
      reach, 
      teamId, 
      status,
      lastMaintenanceDate,
      nextMaintenanceDate
    } = body;

    if (!name) {
      return new NextResponse("Name is required", { status: 400 });
    }

    if (!type) {
      return new NextResponse("Type is required", { status: 400 });
    }

    if (!teamId) {
      return new NextResponse("Team ID is required", { status: 400 });
    }

    // Get the user from the database
    const dbUser = await db.query.users.findFirst({
      where: eq(users.clerkId, userId),
    });

    if (!dbUser) {
      return new NextResponse("User not found", { status: 404 });
    }

    // Verify the user is a member of the team with appropriate role
    const teamMembership = await db.query.teamMembers.findFirst({
      where: and(
        eq(teamMembers.userId, dbUser.id),
        eq(teamMembers.teamId, teamId)
      ),
    });

    if (!teamMembership || !["admin", "owner", "manager"].includes(teamMembership.role)) {
      return new NextResponse("Not authorized to add cranes", { status: 403 });
    }

    // Create the crane with a UUID
    const craneId = uuidv4();
    
    // Use raw SQL to insert with a specific ID
    const newCrane = await db.execute(sql`
      INSERT INTO cranes (
        id, name, model, type, capacity, height, reach, team_id,
        status, last_maintenance_date, next_maintenance_date, created_at, updated_at
      ) VALUES (
        ${craneId}, ${name}, ${model || null}, ${type}, ${capacity || null}, 
        ${height || null}, ${reach || null}, ${teamId},
        ${status || "available"}, 
        ${lastMaintenanceDate ? new Date(lastMaintenanceDate) : null}, 
        ${nextMaintenanceDate ? new Date(nextMaintenanceDate) : null},
        ${new Date()}, ${new Date()}
      ) RETURNING *
    `);

    return NextResponse.json(newCrane);
  } catch (error) {
    console.error("[CRANES_POST]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}
