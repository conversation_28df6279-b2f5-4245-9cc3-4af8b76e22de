import { NextResponse } from 'next/server';
import { auth, currentUser, clerkClient } from '@clerk/nextjs/server';
import { db } from '@/lib/db';
import { users } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export async function POST(request: Request) {
  try {
    const session = await auth();
    const userId = session?.userId;
    const user = await currentUser();
    
    // Check if user is authenticated
    if (!userId || !user) {
      return new NextResponse(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
      });
    }

    // Parse request body
    const body = await request.json();
    const { role, companyName } = body;

    // Validate required fields
    if (!role) {
      return new NextResponse(JSON.stringify({ error: 'Role is required' }), {
        status: 400,
      });
    }

    // Validate role is one of the allowed values
    const allowedRoles = ['admin', 'manager', 'operator', 'subcontractor'];
    if (!allowedRoles.includes(role)) {
      return new NextResponse(JSON.stringify({ error: 'Invalid role' }), {
        status: 400,
      });
    }
    
    // Update user metadata in Clerk
    if (userId) {
      await clerkClient.users.updateUserMetadata(userId, {
        publicMetadata: {
          role,
          companyName,
          onboardedAt: new Date().toISOString(),
        },
      });
    }
    
    // Check if user already exists in our database
    const existingUser = await db.query.users.findFirst({
      where: eq(users.clerkId, userId),
    });

    if (existingUser) {
      // Update existing user
      await db.update(users)
        .set({
          role,
          companyName,
          updatedAt: new Date(),
        })
        .where(eq(users.clerkId, userId));
    } else {
      // Create new user
      await db.insert(users).values({
        clerkId: userId,
        email: user.emailAddresses[0].emailAddress,
        name: `${user.firstName || ''} ${user.lastName || ''}`.trim(),
        companyName,
        role,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }

    return new NextResponse(JSON.stringify({
      success: true,
      message: 'Onboarding completed successfully',
    }), { status: 200 });
    
  } catch (error) {
    console.error('Onboarding error:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
    });
  }
}
