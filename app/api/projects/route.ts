import { NextRequest, NextResponse } from "next/server";
import { auth, currentUser } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { projects, organization, users } from "@/lib/db/schema";
import { eq, and, isNull, sql } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

// GET /api/projects - Get all projects for the current user's team
export async function GET(req: NextRequest) {
  try {
    const { userId } = await auth();
    const user = await currentUser();

    if (!userId || !user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Get the user from the database
    const dbUser = await db.query.users.findFirst({
      where: eq(users.clerkId, userId),
    });

    if (!dbUser) {
      return new NextResponse("User not found", { status: 404 });
    }

    // Get all organizations the user has access to
    // For now, we'll assume the user has access to all organizations
    // In a real implementation, you would check organization membership
    
    const userProjects = await db.query.projects.findMany({
      where: isNull(projects.deletedAt),
      with: {
        organization: true,
      },
      orderBy: (project) => [project.createdAt],
    });

    return NextResponse.json({ projects: userProjects });
  } catch (error) {
    console.error("[PROJECTS_GET]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

// POST /api/projects - Create a new project
export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();
    const user = await currentUser();

    if (!userId || !user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const body = await req.json();
    const { name, description, organizationId, location, startDate, endDate, status } = body;

    if (!name) {
      return new NextResponse("Name is required", { status: 400 });
    }

    if (!organizationId) {
      return new NextResponse("Organization ID is required", { status: 400 });
    }

    // Get the user from the database
    const dbUser = await db.query.users.findFirst({
      where: eq(users.clerkId, userId),
    });

    if (!dbUser) {
      return new NextResponse("User not found", { status: 404 });
    }

    // Verify the organization exists
    const org = await db.query.organization.findFirst({
      where: eq(organization.id, organizationId),
    });

    if (!org) {
      return new NextResponse("Organization not found", { status: 404 });
    }

    // In a real implementation, you would check if the user has permission to create projects in this organization
    
    // Create the project with a UUID
    const projectId = uuidv4();
    
    // Use raw SQL to insert with a specific ID
    const newProject = await db.execute(sql`
      INSERT INTO projects (
        id, name, description, organization_id, created_by, 
        location, safety_compliance, start_date, end_date, 
        status, created_at, updated_at
      ) VALUES (
        ${projectId}, ${name}, ${description || null}, ${organizationId}, ${dbUser.id.toString()},
        ${location ? JSON.stringify(location) : null}, ${null}, 
        ${startDate ? new Date(startDate) : null}, ${endDate ? new Date(endDate) : null},
        ${status || "active"}, ${new Date()}, ${new Date()}
      ) RETURNING *
    `);

    return NextResponse.json(newProject[0]);
  } catch (error) {
    console.error("[PROJECTS_POST]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}
