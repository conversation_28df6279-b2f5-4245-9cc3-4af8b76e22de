# Codebase Summary: LiftrUP

## Key Components and Their Interactions

### Frontend Components

- **Dashboard**: Central hub for users to access all functionality
- **Project Management**: Interface for creating and managing construction projects
- **Crane Calendar**: Interactive scheduling system with conflict detection
- **User Management**: Role-based access control for different stakeholders
- **Analytics**: Reporting and visualization of crane utilization data

### Backend Services

- **Authentication Service**: User management and access control via Clerk
- **Project Service**: CRUD operations for construction projects
- **Booking Service**: Scheduling logic and conflict resolution
- **Payment Service**: Subscription management via Stripe
- **Analytics Service**: Data aggregation and reporting

## Data Flow

1. Users authenticate through Clerk authentication system
2. Role-based permissions determine accessible features
3. Project and crane data stored in PostgreSQL via Drizzle ORM
4. Booking requests validated for conflicts before persistence
5. Analytics data aggregated from booking and project information
6. Subscription status verified for feature access

## External Dependencies

- **Clerk**: Authentication and user management
- **Stripe**: Payment processing and subscription management
- **NeonDB**: PostgreSQL database hosting
- **Vercel**: Application hosting and deployment
- **FullCalendar.js**: Calendar visualization and interaction

## Recent Significant Changes

- Initial project setup with NextJS template
- Documentation structure established
- Clerk authentication fully integrated
  - User sign-up and sign-in flows configured
  - Role-based access control implemented
  - Protected routes and middleware setup
  - User onboarding flow created
  - Webhook handler for user synchronization
- Dashboard layout and navigation implemented
  - Role-based sidebar navigation
  - User profile component with role display
  - Settings and help pages created
- Database schema updated to support authentication
  - User table with Clerk ID integration
  - Company and role fields added

## User Feedback Integration

- No user feedback collected yet as the project is in initial development phase
- Planned feedback collection points:
  - Post-MVP release for core scheduling functionality
  - After implementation of analytics features
  - During subscription model testing
