# Technology Stack: LiftrUP

## Frontend

- **Framework**: Next.js (React)
- **Styling**: Tailwind CSS
- **UI Components**: Shadcn UI
- **Calendar Integration**: FullCalendar.js
- **State Management**: React Context API / React Query

## Backend

- **API**: Next.js API routes
- **Database**: PostgreSQL (NeonDB)
- **ORM**: Drizzle
- **Authentication**: Clerk
- **Payment Processing**: Stripe

## Infrastructure

- **Hosting**: Vercel
- **CI/CD**: GitHub Actions
- **Monitoring**: Vercel Analytics

## External Services

- **Email**: SendGrid (planned)
- **File Storage**: AWS S3 (planned)

## Development Tools

- **Package Manager**: npm/yarn
- **Version Control**: Git
- **Code Quality**: ESLint, Prettier
- **Testing**: Jest, React Testing Library (planned)

## Architecture Decisions

- **Authentication Strategy**: Using Clerk for secure authentication and role-based access control
  - Server-side authentication with `auth()` and `currentUser()` functions
  - Client-side components with `<UserButton />` and `useUser()` hook
  - Custom middleware for protected routes and redirects
  - Role-based access control (admin, manager, operator, subcontractor)
  - Webhook integration for user data synchronization
  - User onboarding flow with role selection

- **Database Choice**: PostgreSQL via NeonDB for reliability and scalability
  - Serverless PostgreSQL with automatic scaling
  - Connection pooling for efficient resource usage
  - Branching capability for development and testing

- **ORM Selection**: Drizzle for type-safe database operations
  - Schema-first approach with TypeScript integration
  - Migration system for database versioning
  - Query builder with type safety
  - Relations for complex data modeling

- **UI Component Library**: Shadcn UI for consistent design system and accessibility
  - Accessible components following WAI-ARIA standards
  - Customizable with Tailwind CSS
  - Responsive design patterns

- **Calendar Implementation**: FullCalendar.js for robust scheduling interface
  - Interactive drag-and-drop scheduling
  - Resource allocation visualization
  - Conflict detection capabilities

- **Payment Processing**: Stripe for subscription management and secure payments
  - Subscription management with tiered pricing
  - Secure payment processing
  - Webhook integration for payment events
