# LiftrUP: Product Requirements Document

## 1. Executive Summary

LiftrUP is a specialized construction management SaaS platform designed to eliminate crane scheduling conflicts and downtime. The platform serves construction managers, crane operators, and subcontractors by providing real-time collaborative scheduling and centralized project oversight. LiftrUP replaces inefficient manual spreadsheets and disjointed tools with a unified system for booking, role-based access, and utilization analytics.

**Key Differentiators:**
- Real-time conflict detection and resolution
- Role-based access for all stakeholders
- Comprehensive analytics for crane utilization
- Streamlined communication between teams
- Mobile-friendly interface for on-site use

## 2. Market Analysis

### Problem Statement
Construction sites frequently experience delays and inefficiencies due to:
- Manual crane scheduling leading to conflicts and double-bookings
- Poor visibility into crane availability across teams
- Communication gaps between managers, operators, and subcontractors
- Lack of data for optimizing crane utilization
- Inefficient approval processes for scheduling changes

### Target Market
Primary focus on medium to large construction projects in Australia with:
- Multiple cranes operating simultaneously
- Various subcontractors requiring crane access
- Project durations of 3+ months
- Complex scheduling requirements

### Competitive Landscape
- General project management tools lack construction-specific features
- Existing construction management software focuses broadly without crane specialization
- Spreadsheets and manual systems remain the primary competitors

## 3. User Personas

### Construction Manager
- **Name:** <PERSON>
- **Age:** 42
- **Role:** Senior Construction Manager
- **Goals:**
  - Minimize crane downtime and scheduling conflicts
  - Track project progress against timelines
  - Ensure efficient resource allocation
  - Generate reports for stakeholders
- **Pain Points:**
  - Manual coordination across multiple teams
  - Lack of visibility into crane availability
  - Time wasted resolving scheduling conflicts
  - Difficulty tracking project delays related to crane availability

### Crane Operator/Owner
- **Name:** Sarah Jenkins
- **Age:** 35
- **Role:** Crane Operations Manager
- **Goals:**
  - Optimize crane utilization
  - Reduce idle time between jobs
  - Track maintenance schedules
  - Provide clear availability to teams
- **Pain Points:**
  - Double-bookings causing operational issues
  - Last-minute cancellations or changes
  - Difficulty communicating availability to multiple teams
  - Manual tracking of utilization metrics

### Trade Subcontractor
- **Name:** David Chen
- **Age:** 29
- **Role:** Electrical Subcontractor Lead
- **Goals:**
  - Quickly reserve crane slots for specific tasks
  - Coordinate schedules with other trades
  - Receive timely notifications about changes
  - Minimize crew idle time waiting for crane access
- **Pain Points:**
  - Unclear booking processes
  - Communication gaps with other teams
  - Last-minute schedule changes
  - Difficulty planning work around crane availability

## 4. User Stories and Requirements

### Core User Stories

#### Project Management
1. As a construction manager, I want to create new projects with relevant details so I can organize crane scheduling by project.
   - **Acceptance Criteria:**
     - Create projects with name, location, start/end dates
     - Add project description and key stakeholders
     - Upload project documents and plans
     - Assign specific cranes to projects

2. As a construction manager, I want to view a dashboard of all active projects so I can monitor overall progress.
   - **Acceptance Criteria:**
     - Display projects in card or list view
     - Show key metrics (timeline, crane utilization)
     - Filter projects by status, date range, or location
     - Quick access to detailed project view

#### Crane Management
3. As a crane operator, I want to add and manage crane details so teams know what equipment is available.
   - **Acceptance Criteria:**
     - Add crane specifications (type, capacity, reach)
     - Set availability windows and maintenance periods
     - Upload crane documentation and certifications
     - Track operational status (active, maintenance, offline)

4. As a construction manager, I want to see all cranes assigned to my project so I can plan resource allocation.
   - **Acceptance Criteria:**
     - List view of all cranes with key specifications
     - Filter by availability, type, or capacity
     - View maintenance schedule
     - Access detailed crane information

#### Booking System
5. As a subcontractor, I want to book crane time slots using a calendar interface so I can secure resources for my team.
   - **Acceptance Criteria:**
     - Drag-and-drop booking creation
     - Set duration, purpose, and location
     - Receive immediate feedback on availability
     - Submit for approval if required by workflow

6. As a construction manager, I want to be alerted about scheduling conflicts so I can resolve them proactively.
   - **Acceptance Criteria:**
     - Real-time conflict detection
     - Visual indicators for overlapping bookings
     - Notification to affected parties
     - Suggested alternative time slots

#### Collaboration
7. As a project admin, I want to invite team members with specific roles so they have appropriate access levels.
   - **Acceptance Criteria:**
     - Send email invitations with role assignment
     - Set permissions by role (admin, manager, operator, subcontractor)
     - Allow role changes for existing users
     - Deactivate users when no longer needed

8. As a subcontractor, I want to receive notifications about schedule changes so I can adjust my team's plans.
   - **Acceptance Criteria:**
     - In-app notifications for relevant changes
     - Email alerts for critical updates
     - Customizable notification preferences
     - Clear indication of what changed and why

#### Analytics
9. As a construction manager, I want to generate utilization reports so I can optimize crane usage.
   - **Acceptance Criteria:**
     - Visual charts of utilization by crane, project, or time period
     - Identify peak usage times and idle periods
     - Export data in CSV/PDF formats
     - Compare actual vs. planned utilization

10. As a crane operator, I want to track operational metrics so I can improve efficiency.
    - **Acceptance Criteria:**
      - Hours of operation vs. idle time
      - Booking fulfillment rate
      - Maintenance impact on availability
      - Comparison across time periods

## 5. Technical Specifications

### Architecture
- **Frontend:** Next.js (React) with Tailwind CSS and Shadcn UI components
- **Backend:** Next.js API routes with serverless functions
- **Database:** PostgreSQL via NeonDB with Drizzle ORM
- **Authentication:** Clerk for user management and role-based access
- **Payment Processing:** Stripe for subscription management

### Data Models

#### User
- ID, name, email, phone
- Role (admin, manager, operator, subcontractor)
- Associated projects
- Notification preferences

#### Project
- ID, name, description, location
- Start date, end date, status
- Associated users and roles
- Assigned cranes

#### Crane
- ID, name, type, capacity
- Specifications (reach, height, etc.)
- Status, maintenance schedule
- Associated projects

#### Booking
- ID, crane ID, project ID
- Start time, end time, duration
- Purpose, location on site
- Requested by, approved by
- Status (pending, approved, completed, cancelled)

#### Subscription
- ID, organization ID
- Plan type, billing cycle
- Payment status, history
- Features enabled

### API Endpoints

#### Authentication
- `/api/auth/*` - Clerk authentication endpoints

#### Projects
- `GET /api/projects` - List all accessible projects
- `POST /api/projects` - Create new project
- `GET /api/projects/:id` - Get project details
- `PUT /api/projects/:id` - Update project
- `DELETE /api/projects/:id` - Archive project

#### Cranes
- `GET /api/cranes` - List all cranes
- `POST /api/cranes` - Add new crane
- `GET /api/cranes/:id` - Get crane details
- `PUT /api/cranes/:id` - Update crane
- `GET /api/cranes/:id/availability` - Get availability

#### Bookings
- `GET /api/bookings` - List bookings with filters
- `POST /api/bookings` - Create booking
- `GET /api/bookings/:id` - Get booking details
- `PUT /api/bookings/:id` - Update booking
- `DELETE /api/bookings/:id` - Cancel booking
- `POST /api/bookings/:id/approve` - Approve booking

#### Analytics
- `GET /api/analytics/utilization` - Crane utilization data
- `GET /api/analytics/projects` - Project timeline analytics
- `GET /api/analytics/export` - Generate exportable reports

#### Subscriptions
- `/api/stripe/checkout` - Create checkout session
- `/api/stripe/webhook` - Handle Stripe events
- `GET /api/subscription` - Get current subscription
- `PUT /api/subscription` - Update subscription

## 6. User Interface Design

### Design System
- **Color Palette:**
  - Primary: Professional blue (#2A5C8F)
  - Secondary: Light gray (#F5F7FA)
  - Accent: Green (#4CAF50) for success/notifications
  - Warning: Amber (#FFC107)
  - Error: Red (#F44336)
- **Typography:**
  - Primary font: Inter (sans-serif)
  - Headings: 24px/20px/18px
  - Body: 16px/14px
  - Small text: 12px
- **Components:**
  - Shadcn UI as base component library
  - Custom calendar components with FullCalendar.js
  - Data tables with sorting and filtering
  - Modal dialogs for quick actions
  - Toast notifications for system messages

### Key Screens

#### Dashboard
- Project overview cards
- Quick access to recent bookings
- Utilization charts and metrics
- Activity feed of recent changes
- Alert section for conflicts or issues

#### Project Detail
- Project information and timeline
- Tabs for cranes, bookings, team members
- Activity log for project-specific events
- Document repository
- Project-specific analytics

#### Crane Calendar
- Week/month view of all bookings
- Color-coding by project or subcontractor
- Drag-and-drop interface for booking
- Filters for specific cranes or projects
- Conflict indicators and warnings

#### Booking Management
- Form for creating/editing bookings
- Conflict detection visualization
- Approval workflow indicators
- Related bookings and dependencies
- Communication thread for booking-specific notes

#### Analytics Dashboard
- Utilization charts by crane, project, or time
- Heatmap of peak usage times
- Comparison charts (planned vs. actual)
- Exportable reports with customizable parameters
- Filters for date ranges, projects, or cranes

## 7. Subscription and Pricing

### Tiered Model

#### LiftrBasic - $99/month (AUD)
- 1 project
- Up to 3 cranes per project
- Basic booking system
- Email notifications
- Standard reports

#### LiftrPro - $299/month (AUD)
- 3 projects
- Up to 5 cranes per project
- Advanced booking with conflict resolution
- In-app and email notifications
- Custom reports and analytics
- API access

#### Enterprise - Custom pricing
- Unlimited projects
- Unlimited cranes
- All features included
- Priority support
- Custom integrations
- Dedicated account manager

### Additional Revenue
- Overage fees for exceeding project/crane limits
- One-time onboarding fee for setup and training ($500)
- Future add-ons like AI scheduling optimization ($50/month)

## 8. Implementation Roadmap

### Phase 1: MVP (Weeks 1-4)
- Core authentication with Clerk
- Basic project and crane management
- Simple booking calendar
- Fundamental conflict detection
- Basic subscription with Stripe

### Phase 2: Enhanced Features (Weeks 5-8)
- Advanced booking system with drag-and-drop
- Improved conflict resolution
- Role-based permissions
- Email notifications
- Basic reporting

### Phase 3: Analytics and Optimization (Weeks 9-12)
- Comprehensive analytics dashboard
- Export functionality
- Advanced filters and search
- Mobile responsiveness improvements
- Performance optimization

### Phase 4: Advanced Features (Weeks 13-16)
- API for external integrations
- Advanced reporting
- Bulk operations
- Document management
- Enterprise features

## 9. Success Metrics

### Key Performance Indicators
- User adoption rate (target: 80% of invited users active)
- Booking conflict reduction (target: 90% reduction vs. manual methods)
- Time saved in scheduling (target: 5+ hours per week per project)
- Crane utilization improvement (target: 15% increase)
- Customer retention rate (target: 85% after 12 months)

### Monitoring Plan
- Weekly usage analytics review
- Monthly customer feedback surveys
- Quarterly feature prioritization based on usage data
- Ongoing performance monitoring

## 10. Risks and Mitigations

### Identified Risks
1. **User Adoption:** Construction industry traditionally slow to adopt new technology
   - **Mitigation:** Intuitive UI, comprehensive onboarding, familiar spreadsheet-like views

2. **Internet Connectivity:** Construction sites may have limited connectivity
   - **Mitigation:** Offline mode for critical functions, low-bandwidth optimizations

3. **Complex Scheduling Logic:** Edge cases in conflict detection
   - **Mitigation:** Thorough testing with real-world scenarios, manual override options

4. **Integration Challenges:** Existing systems may require custom connections
   - **Mitigation:** Well-documented API, common export formats, integration specialists

5. **Scalability Concerns:** Performance with many concurrent users/projects
   - **Mitigation:** Database optimization, caching strategies, load testing

## 11. Appendix

### Glossary of Terms
- **Booking:** A reserved time slot for crane usage
- **Conflict:** Overlapping crane bookings
- **Utilization:** Percentage of available time a crane is in use
- **Role:** User permission level (admin, manager, operator, subcontractor)

### References
- Australian construction industry standards
- Crane safety regulations
- Project management best practices

### Future Considerations
- Mobile app development
- IoT integration with crane telemetry
- AI-powered scheduling recommendations
- Equipment maintenance prediction
- Multi-site project management
