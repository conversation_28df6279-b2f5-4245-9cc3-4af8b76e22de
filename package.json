{"private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "db:setup": "npx tsx lib/db/setup.ts", "db:seed": "npx tsx lib/db/seed.ts", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio"}, "dependencies": {"@clerk/nextjs": "^6.21.0", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.12", "@tailwindcss/postcss": "4.1.7", "@types/node": "^22.15.18", "@types/react": "19.1.4", "@types/react-dom": "19.1.5", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "drizzle-kit": "^0.31.1", "drizzle-orm": "^0.43.1", "jose": "^6.0.11", "lucide-react": "^0.511.0", "next": "15.4.0-canary.47", "postcss": "^8.5.3", "postgres": "^3.4.5", "react": "19.1.0", "react-dom": "19.1.0", "server-only": "^0.0.1", "sonner": "^1.7.1", "stripe": "^18.1.0", "svix": "^1.67.0", "swr": "^2.3.3", "tailwind-merge": "^3.3.0", "tailwindcss": "4.1.7", "tw-animate-css": "^1.3.0", "typescript": "^5.8.3", "uuid": "^11.1.0", "zod": "^3.24.4"}, "devDependencies": {"dotenv": "^16.5.0"}}