import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from '@/lib/db/schema';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { sql } from 'drizzle-orm';

// Load environment variables
dotenv.config();

async function applyMigrations() {
  // Check for POSTGRES_URL
  if (!process.env.POSTGRES_URL) {
    throw new Error('POSTGRES_URL environment variable is not defined');
  }
  
  // Create a new database connection
  const client = postgres(process.env.POSTGRES_URL);
  const db = drizzle(client, { schema });
  try {
    console.log('Starting database migrations...');
    
    // Path to the migration file
    const migrationPath = path.join(process.cwd(), 'lib/db/migrations/0002_update_existing_tables.sql');
    
    // Read the SQL file
    const migrationSql = fs.readFileSync(migrationPath, 'utf8');
    
    // Split the SQL by statement breakpoints
    const statements = migrationSql.split('--> statement-breakpoint');
    
    console.log(`Found ${statements.length} SQL statements to execute`);
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i].trim();
      if (statement) {
        console.log(`Executing statement ${i + 1}/${statements.length}`);
        try {
          await db.execute(sql.raw(statement));
          console.log(`Statement ${i + 1} executed successfully`);
        } catch (error) {
          console.error(`Error executing statement ${i + 1}:`, error);
          // Continue with other statements even if one fails
        }
      }
    }
    
    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    // Close the database connection
    await client.end();
    process.exit(0);
  }
}

applyMigrations();
