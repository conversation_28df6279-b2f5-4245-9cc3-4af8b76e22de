import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from '@/lib/db/schema';
import dotenv from 'dotenv';
import { sql } from 'drizzle-orm';

// Load environment variables
dotenv.config();

async function checkSchema() {
  // Check for POSTGRES_URL
  if (!process.env.POSTGRES_URL) {
    throw new Error('POSTGRES_URL environment variable is not defined');
  }
  
  // Create a new database connection
  const client = postgres(process.env.POSTGRES_URL);
  const db = drizzle(client, { schema });

  try {
    console.log('Checking database schema...');
    
    // Check tables structure
    const tables = await db.execute(sql`
      SELECT 
        table_name, 
        column_name, 
        data_type, 
        is_nullable
      FROM 
        information_schema.columns
      WHERE 
        table_schema = 'public'
      ORDER BY 
        table_name, ordinal_position
    `);
    
    console.log('Tables and columns:');
    console.log(JSON.stringify(tables, null, 2));
    
    // Check foreign keys
    const foreignKeys = await db.execute(sql`
      SELECT
        tc.table_name, 
        kcu.column_name, 
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name 
      FROM 
        information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
          AND ccu.table_schema = tc.table_schema
      WHERE tc.constraint_type = 'FOREIGN KEY'
      ORDER BY tc.table_name, kcu.column_name
    `);
    
    console.log('\nForeign keys:');
    console.log(JSON.stringify(foreignKeys, null, 2));
    
  } catch (error) {
    console.error('Schema check failed:', error);
  } finally {
    // Close the database connection
    await client.end();
    process.exit(0);
  }
}

checkSchema();
