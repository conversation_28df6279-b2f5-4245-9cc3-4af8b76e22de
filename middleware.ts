import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';

// Define public routes that don't require authentication
const publicRoutes = [
  '/',
  '/sign-in',
  '/sign-up',
  '/pricing',
  '/about',
  '/contact',
  '/terms',
  '/privacy',
];

// Define webhook routes that should bypass Clerk's middleware completely
const webhookRoutes = [
  '/api/webhook/clerk',
  '/api/webhook/stripe',
];

// Create route matchers
const isPublicRoute = createRouteMatcher(publicRoutes);
const isWebhookRoute = createRouteMatcher(webhookRoutes);

// Export the Clerk middleware with a custom handler
export default clerkMiddleware((auth, req) => {
  // Check if it's a webhook route that should bypass authentication
  if (isWebhookRoute(req)) {
    return NextResponse.next();
  }
  
  // For public routes, allow access without authentication
  if (isPublicRoute(req)) {
    return NextResponse.next();
  }
  
  // For protected routes, check if user is authenticated
  if (!auth.userId) {
    const signInUrl = new URL('/sign-in', req.url);
    signInUrl.searchParams.set('redirect_url', req.url);
    return NextResponse.redirect(signInUrl);
  }
  
  // User is authenticated, allow access to protected route
  return NextResponse.next();
});

export const config = {
  matcher: [
    '/((?!.+\\.[\\w]+$|_next).*)', // match all paths not ending in file extension or starting with _next
    '/', // match the root path
    '/(api|trpc)(.*)', // match API and tRPC routes
  ],
};
